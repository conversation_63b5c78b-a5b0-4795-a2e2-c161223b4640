select
    `a`.`EmpNo` AS `EMP_CODE`,
    regexp_replace (`a`.`Title`, '[0-9]', '') AS `FULL_NAME`,
    left (regexp_replace (`a`.`Title`, '[0-9]', ''), 1) AS `FIRST_NAME`,
    substr (regexp_replace (`a`.`Title`, '[0-9]', ''), 2) AS `LAST_NAME`,
    `c`.`EmpNo` AS `LEADER_CODE`,
    `f`.`Account` AS `LOGIN_NAME`,
    `c`.`Title` AS `LEADER_FULL_NAME`,
    `a`.`Gender` AS `GENDER_CODE`,
    `mc1`.`Title` AS `GENDER`,
    `a`.`Nation` AS `NATION_CODE`,
    `mc2`.`Title` AS `NATION`,
    date_format (`a`.`BirthDate`, '%Y/%m/%d %H:%i:%S') AS `BIRTHDAY`,
    `a`.`MaritalStatus` AS `MARITAL_STATUS_CODE`,
    `mc3`.`Title` AS `MARITAL_STATUS`,
    `a`.`MobileNo` AS `MOBILE_PHONE`,
    `a`.`TelNo` AS `COMPANY_PHONE`,
    `a`.`Email` AS `EMAIL`,
    `a`.`IDType` AS `ID_TYPE_CODE`,
    `mc4`.`Title` AS `ID_TYPE`,
    `a`.`IDNo` AS `ID_NUMBER`,
    NULL AS `id_validity`,
    `a`.`Party` AS `POLITICS_STATUS_CODE`,
    `mc5`.`Title` AS `POLITICS_STATUS`,
    ifnull (`a`.`Disabled`, 0) AS `STATUS_CODE`,
    (
        case
            when (`a`.`Disabled` = 1) then '离职'
            else '现职'
        end
    ) AS `STATUS`,
    date_format (`a`.`CreateDate`, '%Y/%m/%d %H:%i:%S') AS `HIRE_DATE`,
    date_format (`a`.`DisabledDate`, '%Y/%m/%d %H:%i:%S') AS `QUIT_DATE`,
    `a`.`postM2` AS `POSITION_LEVEL_CODE`,
    `mc6`.`Title` AS `POSITION_LEVEL`,
    `e`.`PostCustom4` AS `POSITION_TYPE_CODE`,
    `mc8`.`Title` AS `POSITION_TYPE`,
    `a`.`EmpType` AS `WORK_TYPE_CODE`,
    `mc7`.`Title` AS `WORK_TYPE`,
    ifnull (
        date_format (`clehr`.`d`.`begindate`, '%Y/%m/%d %H:%i:%S'),
        date_format (`a`.`CreateDate`, '%Y/%m/%d %H:%i:%S')
    ) AS `HR_LAST_DATE`,
    date_format (`a`.`ProbEndDate`, '%Y/%m/%d %H:%i:%S') AS `BECOME_REG_DATE`,
    `a`.`PorbStatus` AS `REG_STATUS_CODE`,
    `mc10`.`Title` AS `REG_STATUS`,
    `a`.`ConUnit` AS `CONTRACT_SUBJECT_CODE`,
    `mc9`.`Title` AS `CONTRACT_SUBJECT`,
    `clehr`.`md_address`.`code` AS `WORK_PLACE_CODE`,
    `clehr`.`md_address`.`title` AS `WORK_PLACE`
from
    (
        (
            (
                (
                    (
                        (
                            (
                                (
                                    (
                                        (
                                            (
                                                (
                                                    (
                                                        (
                                                            (
                                                                (
                                                                    (
                                                                        `clehr`.`md_employee` `a`
                                                                        left join `clehr`.`md_organization` `b` on ((`a`.`OrgID` = `b`.`OrgID`))
                                                                    )
                                                                    left join `clehr`.`md_employee` `c` on ((`a`.`ParentID` = `c`.`EmpID`))
                                                                )
                                                                left join `clehr`.`mv_emp_event` `d` on ((`a`.`EmpID` = `clehr`.`d`.`EmpID`))
                                                            )
                                                            left join `clehr`.`md_position` `e` on ((`a`.`PostID` = `e`.`PostID`))
                                                        )
                                                        left join `clehr`.`mc_users` `f` on (
                                                            (
                                                                (`a`.`EmpID` = `f`.`EmpID`)
                                                                and (`f`.`Account` <> 'admin')
                                                            )
                                                        )
                                                    )
                                                    left join `clehr`.`md_codes` `mc1` on ((`mc1`.`ID` = `a`.`Gender`))
                                                )
                                                left join `clehr`.`md_codes` `mc2` on ((`mc2`.`ID` = `a`.`Nation`))
                                            )
                                            left join `clehr`.`md_codes` `mc3` on ((`mc3`.`ID` = `a`.`MaritalStatus`))
                                        )
                                        left join `clehr`.`md_codes` `mc4` on ((`mc4`.`ID` = `a`.`IDType`))
                                    )
                                    left join `clehr`.`md_codes` `mc5` on ((`mc5`.`ID` = `a`.`Party`))
                                )
                                left join `clehr`.`md_codes` `mc6` on ((`mc6`.`ID` = `a`.`postM2`))
                            )
                            left join `clehr`.`md_codes` `mc7` on ((`mc7`.`ID` = `a`.`EmpType`))
                        )
                        left join `clehr`.`md_codes` `mc8` on ((`mc8`.`ID` = `e`.`PostCustom4`))
                    )
                    left join `clehr`.`md_codes` `mc9` on ((`mc9`.`ID` = `a`.`ConUnit`))
                )
                left join `clehr`.`md_codes` `mc10` on ((`mc10`.`ID` = `a`.`PorbStatus`))
            )
            left join `clehr`.`md_address` on ((`clehr`.`md_address`.`id` = `a`.`WorkCity`))
        )
        left join `clehr`.`md_organization` on ((`clehr`.`md_organization`.`OrgID` = `a`.`OrgID`))
    )