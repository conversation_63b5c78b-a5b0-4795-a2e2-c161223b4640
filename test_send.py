
import unittest
from unittest.mock import patch
from send import get_access_token, build_message_body, send_message

class TestWeChatMessage(unittest.TestCase):
    def setUp(self):
        self.corpid = "test_corpid"
        self.corpsecret = "test_corpsecret"
        self.agentid = 1000001

    def test_build_message_body(self):
        message = build_message_body(
            touser="TestUser",
            msgtype="text",
            agentid=self.agentid,
            text="Test message"
        )
        self.assertEqual(message["touser"], "TestUser")
        self.assertEqual(message["msgtype"], "text")
        self.assertEqual(message["agentid"], self.agentid)
        self.assertEqual(message["text"]["content"], "Test message")

    @patch('requests.get')
    def test_get_access_token(self, mock_get):
        # Mock the response
        mock_get.return_value.json.return_value = {"access_token": "test_token"}
        
        token = get_access_token(self.corpid, self.corpsecret)
        self.assertEqual(token, "test_token")

    @patch('requests.post')
    def test_send_message(self, mock_post):
        # Mock the response
        mock_post.return_value.json.return_value = {"errcode": 0, "errmsg": "ok"}
        
        message = build_message_body(
            touser="TestUser",
            msgtype="text",
            agentid=self.agentid,
            text="Test message"
        )
        result = send_message("test_token", message)
        self.assertEqual(result["errcode"], 0)

if __name__ == "__main__":
    unittest.main()