﻿-- 
-- <PERSON>ript was generated by Devart dbForge Studio for MySQL, Version *********
-- Product Home Page: http://www.devart.com/dbforge/mysql/studio
-- Script date 2025/6/27 20:43:58
-- Source server version: 8.0.28
-- Source connection string: User Id=rw_clehr;Host=*************;Port=3307;Database=clehr;Character Set=utf8
-- Target server version: 8.0.39
-- Target connection string: User Id=rw_clehr;Host=*************;Port=3308;Character Set=utf8
-- Run this script against clehr to synchronize it with clehr
-- 


--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;



--
-- Drop procedure `wfsp_empchange_initsub`
--
DROP PROCEDURE wfsp_empchange_initsub;

DELIMITER $$

--
-- Create procedure `wfsp_empchange_initsub`
--
CREATE PROCEDURE wfsp_empchange_initsub(
	in p_instanceid int,
	out p_retval int
)
BEGIN
			insert into wf_ht_empchange_subb(wfinstanceid,handoveremp,V2)
			select p_instanceid,approver,a.V2
			from 
		    (select substring_index(substring_index(a.handoveremp,',',b.help_topic_id+1),',',-1) as approver  ,A.V2
            from  wf_ht_empchange a 
              left join   mysql.help_topic b on b.help_topic_id < (length(a.handoveremp) - length(replace(a.handoveremp,',',''))+1)
              where a.wfinstanceid=p_instanceid ) a
	    	where not exists(select 1 from wf_ht_empchange_subb b where b.wfinstanceid=p_instanceid and b.handoveremp=a.approver);
		
			delete from wf_ht_empchange_subb a where a.wfinstanceid=p_instanceid and a.handoveremp not in (select substring_index(substring_index(a.handoveremp,',',b.help_topic_id+1),',',-1) as approver  from  wf_ht_empchange a left join   mysql.help_topic b on b.help_topic_id < (length(a.handoveremp) - length(replace(a.handoveremp,',',''))+1) where a.wfinstanceid=p_instanceid );
		
	set p_retval=0;	
		
END
$$



DELIMITER ;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;