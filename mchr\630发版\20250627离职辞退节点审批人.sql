﻿USE clehr;

INSERT INTO mc_flowapprover (id, Title, Title1, Title2, Title3, Title4, Title5, SQLtext, remark) VALUES
(55, '资产审核分管领导审核', NULL, NULL, NULL, NULL, NULL, 'select fgempid as empid from eu_assetreviewers WHERE empid= ${empid}\n', NULL);

INSERT INTO mc_flowapprover (id, Title, Title1, Title2, Title3, Title4, Title5, SQLtext, remark) VALUES
(56, '固定角色-职业发展COE', NULL, NULL, NULL, NULL, NULL, 'select substring_index(substring_index(a.approver,'','',b.help_topic_id+1),'','',-1) as empid\nfrom wf_approverconfig a\njoin mysql.help_topic b on b.help_topic_id < (length(a.approver) - length(replace(a.approver,'','',''''))+1) where a.id=24;', '20250620added by summer');
