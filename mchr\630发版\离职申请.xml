<flow approvalhistype="1" approvalhisnotice="1" approvalHisOrder="1" approvalhismerge="0" approvalhistodo="0" approvalhiscols="" popcomment="0" signComment="0" filecomment="4" untodonotice="0" notifycode="1" notifyssokey="" notifyagentid="" notifyimageid="">
  <abstract><![CDATA[eyIwIjoic2VsZWN0IENPTkNBVCgn56a76IGMJywnLScsYS50aXRsZSwn77yIJyxhLmVtcG5vLCfvvIknKVxuZnJvbSB3Zl9odF9lbXBjaGFuZ2UgYVxud2hlcmUgV0ZpbnN0YW5jZUlEPSR7V0ZfSW5zdGFuY2VJRH07In0=]]></abstract>
  <approvalhissql><![CDATA[]]></approvalhissql>
  <taskagentsql><![CDATA[]]></taskagentsql>
  <dealcronsql></dealcronsql>
  <notifycustom><![CDATA[]]></notifycustom>
  <Formlayout>
    <blocks id="1y0vvmkemof" code="WF0310" type="title" border="1" hidden="0"/>
    <blocks id="1pcy3xl0ba1" code="WF0310" type="block" border="0" hidden="0"/>
    <blocks id="28utmccneko" code="WF0321" type="title" border="1" hidden="0"/>
    <blocks id="7eg81fwwh6" code="WF0321" type="block" border="0" hidden="0"/>
  </Formlayout>
  <formtype><![CDATA[forms]]></formtype>
  <flowcustom><![CDATA[]]></flowcustom>
  <node id="1" title="eyIwIjoi5Y+R6LW3In0=" type="1" level="0" left="199" top="41" nodemode="0" approvemode="0" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <dealagent><![CDATA[]]></dealagent>
    <action type="initiate" title="eyIwIjoi5Y+R6LW355Sz6K+3In0=" comments="0" goon="0" anytime="0" confirm="0" cssclass="btn-success" icon="glyphicon glyphicon-send">
      <source><![CDATA[]]></source>
      <showcondi><![CDATA[]]></showcondi>
      <preaction><![CDATA[]]></preaction>
      <doaction><![CDATA[]]></doaction>
    </action>
    <action type="draft" title="eyIwIjoi5L+d5a2Y6I2J56i/In0=" comments="0" cssclass="btn-primary" icon="glyphicon glyphicon-floppy-saved"/>
    <action type="delete" title="eyIwIjoi5Yig6Zmk6I2J56i/In0=" comments="0" cssclass="btn-danger" icon="glyphicon glyphicon-floppy-remove"/>
    <action type="resubmit" title="eyIwIjoi6YeN5paw5o+Q5LqkIn0=" comments="0" goon="0" anytime="0" confirm="0" cssclass="btn-grass" icon="hricon-paperairplane">
      <source><![CDATA[]]></source>
      <showcondi><![CDATA[]]></showcondi>
      <preaction><![CDATA[]]></preaction>
      <doaction><![CDATA[]]></doaction>
    </action>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="1" locked="0" visible="1"/>
      <field colkey="DISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="0" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="0" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="0" visible="0"/>
    </form>
    <form code="WF0321" canadd="1" candelete="1" locked="0" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>wfsp_empleave_beforecheck(${wf_instanceid},${wf_nodeid})</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit>api_FLowPRob(1,81,${WF_INITIATOR},WF_FlowLzft,${WF_INSTANCEID});wf_flowtoback(${WF_FLOWID},${WF_INSTANCEID},${WF_NODEID},${WF_TASKID},${WF_INITIATOR},${WF_INITIATOR},${WF_COMMENTS},2);select 1 as TC;select 1 as TC;WFSP_FlowTaskTo_Id(${WF_INSTANCEID},4);WFSP_FlowTaskTo_JSON(${PARAM4},4);api_MH_TASK2(${PARAM5});EP_OARESULT_TO(${PARAM4},2,${PARAM5},${PARAM6});</aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn>wf_flowtoback(${WF_FLOWID},${WF_INSTANCEID},${WF_NODEID},${WF_TASKID},${WF_INITIATOR},${WF_INITIATOR},${WF_COMMENTS},1);select 1 as TC;select 1 as TC;WFSP_FlowTaskTo_Id(${WF_INSTANCEID},2);WFSP_FlowTaskTo_JSON(${PARAM4},2);api_MH_TASK1(${PARAM5});EP_OARESULT_TO(${PARAM4},1,${PARAM5},${PARAM6});</taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="3" title="eyIwIjoi57uT5p2fIn0=" type="2" level="0" left="519" top="1849" nodemode="0" approvemode="0" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <dealagent><![CDATA[]]></dealagent>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="0" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="0" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="0" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="0" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="4" title="eyIwIjoi5ZGY5bel5YWz57O7QlAifQ==" type="3" level="0" left="182" top="222" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[6-${WF0310.ORGNAME}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSP56a76IGMIn0=" comments="0" goon="0" anytime="0" confirm="0" cssclass="btn-success" icon="iconfont-ming icon-public-right">
      <source><![CDATA[]]></source>
      <showcondi><![CDATA[]]></showcondi>
      <preaction><![CDATA[]]></preaction>
      <doaction><![CDATA[]]></doaction>
    </action>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="1" locked="0" visible="1"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="1" locked="0" visible="1"/>
      <field colkey="QRLZYYSM" required="1" locked="0" visible="1"/>
      <field colkey="SFZFJJBCJ" required="1" locked="0" visible="1"/>
      <field colkey="QRLZYYFL" required="1" locked="0" visible="1"/>
      <field colkey="LEAVEPLANTYPE" required="1" locked="0" visible="1"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="1" locked="0" visible="1"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="5" title="eyIwIjoiSFJCUOe7j+eQhiJ9" type="3" level="0" left="502" top="332" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select emp9 from md_organization where orgid=${wf0310.orgname}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="6" title="eyIwIjoi5LqL5Lia6YOo5Lq65Yqb5YiG566hIn0=" type="3" level="0" left="503" top="431" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select emp10 from md_organization where orgid=${wf0310.orgname}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="7" title="eyIwIjoi6Jaq6YWsQ09FIn0=" type="3" level="0" left="726" top="432" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=10]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="1" locked="0" visible="1"/>
      <field colkey="REMARK" required="1" locked="0" visible="1"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>wfsp_empleave_beforecheck(${wf_instanceid},${wf_nodeid})</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="8" title="eyIwIjoi5rGH6IGa6IqC54K5In0=" type="3" level="0" left="503" top="557" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="1" locked="0" visible="1"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="ASSETOVERDUE" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>wfsp_empleave_beforecheck(${wf_instanceid},${wf_nodeid})</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit>wfsp_empchange_initsub(${wf_instanceid})</aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="9" title="eyIwIjoiSFLmgLvnu4/nkIYifQ==" type="3" level="0" left="45" top="769" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[39-${WF0310.P_EMPID}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="1" locked="0" visible="1"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="1" locked="0" visible="1"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="10" title="eyIwIjoiSFLliIbnrqHpooblr7wifQ==" type="3" level="0" left="635" top="856" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=17]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="11" title="eyIwIjoi5YWs5Y+45oC757uP55CGIn0=" type="3" level="0" left="386" top="975" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=18]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit>WfSp_empleavedo(${wf_instanceid},1)</aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="14" title="eyIwIjoi5ZGY5bel5pys5Lq65aGr5YaZ5bel5L2c5Lqk5o6l5riF5Y2VIn0=" type="3" level="0" left="765" top="1197" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select ${wf0310.p_empid}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5o+Q5LqkIn0=" comments="0" goon="0" anytime="0" confirm="0" cssclass="btn-success" icon="glyphicon glyphicon-ok">
      <source><![CDATA[]]></source>
      <showcondi><![CDATA[]]></showcondi>
      <preaction><![CDATA[]]></preaction>
      <doaction><![CDATA[]]></doaction>
    </action>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="0" locked="1" visible="1"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="1" locked="0" visible="1"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="TRANSFERLIST" required="1" locked="0" visible="1"/>
      <field colkey="ISHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVERATTACH" required="1" locked="0" visible="1"/>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="15" title="eyIwIjoi57un5Lu76ICF56Gu6K6kIn0=" type="3" level="0" left="765" top="1327" nodemode="2" approvemode="1" approveperc="100" approverType="0" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="1" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select substring_index(substring_index(${wf0310.handoveremp},',',b.help_topic_id+1),',',-1) as approver from   mysql.help_topic b where  b.help_topic_id < (length(${wf0310.handoveremp}) - length(replace(${wf0310.handoveremp},',',''))+1)]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5o+Q5LqkIn0=" comments="0" goon="0" anytime="0" confirm="0" cssclass="btn-success" icon="glyphicon glyphicon-ok">
      <source><![CDATA[]]></source>
      <showcondi><![CDATA[]]></showcondi>
      <preaction><![CDATA[]]></preaction>
      <doaction><![CDATA[]]></doaction>
    </action>
    <action type="returnprev" title="eyIwIjoi5Zue6YCA5LiK5LiA57qnIn0=" comments="0" goon="0" anytime="0" confirm="0" cssclass="btn-lightred" icon="glyphicon glyphicon-backward" signedtype="1" signedmode="1">
      <source><![CDATA[]]></source>
      <showcondi><![CDATA[]]></showcondi>
      <preaction><![CDATA[]]></preaction>
      <doaction><![CDATA[]]></doaction>
    </action>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="0" locked="1" visible="1"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[handoveremp=${WF_RECEIVER}]]></linkcondi>
      <field colkey="ISHANDOVER" required="1" locked="0" visible="1"/>
    </form>
    <event/>
  </node>
  <node id="22" title="eyIwIjoi6LSi5Yqh57uT566XIn0=" type="3" level="0" left="118" top="1238" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=6]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="1" locked="0" visible="1"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="0" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="23" title="eyIwIjoi56i95qC457uT566XIn0=" type="3" level="0" left="351" top="1237" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=7]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="1" locked="0" visible="1"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="1" locked="0" visible="1"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="24" title="eyIwIjoi5YWa57uE57uH6L2s5Ye6In0=" type="3" level="0" left="582" top="1238" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=3]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="1" locked="0" visible="1"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="26" title="eyIwIjoi5L+h56eR5b2S6L+Y55S16ISRIn0=" type="3" level="0" left="361" top="1692" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="1" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=2]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="1" locked="0" visible="1"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="28" title="eyIwIjoi5b2S6L+Y5bel54mMIn0=" type="3" level="0" left="116" top="1690" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select substring_index(substring_index(a.approver,',',b.help_topic_id+1),',',-1) as approver from  wf_approverconfig a join   mysql.help_topic b on b.help_topic_id < (length(a.approver) - length(replace(a.approver,',',''))+1) where a.id=1;]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="1" locked="0" visible="1"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="0" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="29" title="eyIwIjoi5ZGK55+l6LSi5YqhIn0=" type="4" level="0" left="695" top="265" nodemode="3" approvemode="0" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" dealauto="0" dealhours="0">
    <receivers><![CDATA[18-${WF0310.P_EMPID}]]></receivers>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="0" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="0" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="0" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="0" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="0" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="0" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="0" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="0" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="0" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="0" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="0" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="0" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="0" visible="0"/>
      <field colkey="REMARK" required="0" locked="0" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="0" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="0" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="0" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="0" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="0" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="0" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="0" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="0" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="0" visible="0"/>
      <field colkey="THEUNION" required="0" locked="0" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="0" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="0" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="0" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="0" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="0" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="0" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="0" visible="0"/>
      <field colkey="TAG1" required="0" locked="0" visible="0"/>
      <field colkey="V1" required="0" locked="0" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="0" visible="0"/>
      <field colkey="V2" required="0" locked="0" visible="0"/>
      <field colkey="V3" required="0" locked="0" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="0" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="30" title="eyIwIjoi56a76IGM5a6h5om577yI6Iez5LqM57qn6YOo6Zeo5YiG566h77yJIn0=" type="3" level="0" left="657" top="617" nodemode="3" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select wffn_flowxh_empleave(${wf_instanceid},0,0)]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="31" title="eyIwIjoi6YOo6Zeo6ZW/In0=" type="3" level="0" left="424" top="693" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select emp1 from md_organization where orgid=${wf0310.depcode}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>wfsp_empleave_beforecheck(${wf_instanceid},${wf_nodeid})</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="32" title="eyIwIjoi5rGH6IGa6IqC54K5In0=" type="3" level="0" left="183" top="1056" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" dealauto="0" dealhours="0">
    <receivers><![CDATA[]]></receivers>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="refuse" title="eyIwIjoi5ouS57udIn0=" comments="1" goon="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="33" title="eyIwIjoi5Z+56K6tQ09FIn0=" type="3" level="0" left="0" top="1237" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=11]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="1" locked="0" visible="1"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="34" title="eyIwIjoi5bel5LyaIn0=" type="3" level="0" left="466" top="1236" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select approver from wf_approverconfig where id=5]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="1" locked="0" visible="1"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="35" title="eyIwIjoi6YOo6Zeo56Gu6K6k5bel5L2c5Lqk5o6lIn0=" type="3" level="0" left="419" top="1477" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select emp1 from md_organization where orgid=${wf0310.depcode}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="CREATEDATE" required="0" locked="1" visible="0"/>
      <field colkey="WORKAGE" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISDEPCHECK" required="1" locked="0" visible="1"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>select 1 ;select 2;XMSP_Api_NEW_LZJY_JSON(${WF_InstanceID});api_NEW_XM_LZJY(${PARAM3});XMSP_Api_LZJY_DOV3(${WF_InstanceID},${PARAM1},${PARAM2},${PARAM3},${PARAM4});</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="36" title="eyIwIjoi5YiG566h5oC7In0=" type="3" level="0" left="800" top="1477" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[select emp2 from md_organization where orgid=${wf0310.depcode}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="1" locked="0" visible="1"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>select 1 ; select 2 ; XMSP_Api_NEW_LZJY_JSON(${WF_InstanceID});api_NEW_XM_LZJY(${PARAM3});XMSP_Api_LZJY_DOV3(${WF_InstanceID},${PARAM1},${PARAM2},${PARAM3},${PARAM4});</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="37" title="eyIwIjoi5rGH6IGa6IqC54K5In0=" type="3" level="0" left="237" top="1551" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" dealauto="0" dealhours="0">
    <receivers><![CDATA[]]></receivers>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="refuse" title="eyIwIjoi5ouS57udIn0=" comments="1" goon="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="38" title="eyIwIjoi5ZGY5bel5YWz57O7SFIg5b2S6L+Y5Yqz5Yqo5omL5YaM77yM5Y+R6YCB56a76IGM6K+B5piOIn0=" type="3" level="0" left="248" top="1837" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[37-${WF_INITIATOR}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="1" locked="0" visible="1"/>
      <field colkey="LABORMANUAL" required="1" locked="0" visible="1"/>
      <field colkey="CERTIFICATE" required="1" locked="0" visible="1"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>wfsp_empleave_beforecheck(${wf_instanceid},${wf_nodeid})</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="39" title="eyIwIjoi5ZGY5bel5YWz57O7QlAifQ==" type="3" level="0" left="399" top="38" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[6-${WF0310.ORGNAME}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="40" title="eyIwIjoi6LSi5YqhIn0=" type="3" level="0" left="649" top="0" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" dealauto="0" dealhours="0">
    <receivers><![CDATA[18-${WF0310.P_EMPID}]]></receivers>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="1" locked="0" visible="1"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="0" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="41" title="eyIwIjoi55u05bGe5b6q546v6Iez6YOo6Zeo6ZW/In0=" type="3" level="0" left="649" top="76" nodemode="3" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" dealauto="0" dealhours="0">
    <receivers><![CDATA[select wffn_flowxh_empleave2(${wf_instanceid},0,0)]]></receivers>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="42" title="eyIwIjoi5rGH6IGa6IqC54K5In0=" type="3" level="0" left="847" top="80" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0" dealopinion="agree">
    <receivers><![CDATA[]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="refuse" title="eyIwIjoi5ouS57udIn0=" comments="1" goon="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive>WfSp_empleavedo(${wf_instanceid},1)</taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="43" title="eyIwIjoi55u05bGe5LiK57qn56Gu6K6k5bel5L2c5Lqk5o6lIn0=" type="3" level="0" left="684" top="1478" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[49-${WF0310.EMPNO}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="CREATEDATE" required="0" locked="1" visible="0"/>
      <field colkey="WORKAGE" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="1" locked="0" visible="1"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>select 1 ; select 2; XMSP_Api_NEW_LZJY_JSON(${WF_InstanceID});api_NEW_XM_LZJY(${PARAM3});XMSP_Api_LZJY_DOV3(${WF_InstanceID},${PARAM1},${PARAM2},${PARAM3},${PARAM4});</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="44" title="eyIwIjoi55u05o6l5LiK57qnIn0=" type="3" level="0" left="0" top="127" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0" dealopinion="agree">
    <receivers><![CDATA[49-${WF0310.EMPNO}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="1" locked="0" visible="1"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="1" locked="0" visible="1"/>
      <field colkey="ASSETREVIEWERS" required="1" locked="0" visible="1"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit>wfsp_empleave_beforecheck(${wf_instanceid},${wf_nodeid})</beforesubmit>
      <beforeback></beforeback>
      <aftersubmit>wfsp_empchange_initsub(${wf_instanceid})</aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="45" title="eyIwIjoi56i95qC46aOO6Zmp5o6S5p+lIn0=" type="3" level="0" left="187" top="373" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[19-${WF0310.P_EMPID}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnprev" title="eyIwIjoi5Zue6YCA5LiK5LiA57qnIn0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="1" locked="0" visible="1"/>
      <field colkey="AUDITDEPARTURE" required="1" locked="0" visible="1"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="1" locked="0" visible="1"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="TRANSFERLIST" required="0" locked="1" visible="0"/>
      <field colkey="ISHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVERATTACH" required="0" locked="1" visible="0"/>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="46" title="eyIwIjoi5Yik5pat6IqC54K5In0=" type="3" level="0" left="501" top="222" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="refuse" title="eyIwIjoi5ouS57udIn0=" comments="1" goon="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="47" title="eyIwIjoi55So5Lq66YOo6Zeo5YiG566hIn0=" type="3" level="0" left="294" top="856" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0" dealopinion="agree">
    <receivers><![CDATA[4-${WF0310.DEPCODE}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="48" title="eyIwIjoi55So5oi36YOo6Zeo5YiG566hIn0=" type="3" level="0" left="456" top="856" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[53-${WF0310.DEPCODE}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="1" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="49" title="eyIwIjoi5ZGY5bel5YWz57O7QlAifQ==" type="4" level="0" left="197" top="127" nodemode="3" approvemode="0" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[6-${WF0310.ORGNAME}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="refuse" title="eyIwIjoi5ouS57udIn0=" comments="1" goon="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="0" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="0" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATE" required="0" locked="0" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="0" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="0" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="0" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="0" visible="0"/>
      <field colkey="REMARK" required="0" locked="0" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="0" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="0" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="0" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="0" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="1" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="0" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="0" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="50" title="eyIwIjoi6LWE5Lqn5a6h5qC4In0=" type="3" level="0" left="0" top="367" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" dealauto="0" dealhours="0">
    <receivers><![CDATA[46-${WF0310.ASSETREVIEWERS}]]></receivers>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnprev" title="eyIwIjoi5Zue6YCA5LiK5LiA57qnIn0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="signed" title="eyIwIjoi5Lya562+In0=" comments="1" cssclass="btn-mint" icon="fa fa-group" signedtype="1" signedmode="1" signedperc="0" signedmulti="0"/>
    <action type="signedagree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="signedrefuse" title="eyIwIjoi5ouS57udIn0=" comments="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="1" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="0" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="0" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="0" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="0" visible="0"/>
      <field colkey="REMARK" required="0" locked="0" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="0" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETNOTES" required="1" locked="0" visible="1"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="1"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="51" title="eyIwIjoi5oub5b6F55So5ZOB5qC46aqMIn0=" type="3" level="0" left="649" top="163" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0" dealopinion="agree">
    <receivers><![CDATA[54-${P_EMPID}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="0" visible="0"/>
      <field colkey="HOSPITALITY" required="1" locked="0" visible="1"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="0" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERFILE" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVERV3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="52" title="eyIwIjoi5oub5b6F55So5ZOBL+WNsOeroC/or4Hnhacv5qGj5qGI5qC46aqMIn0=" type="3" level="0" left="233" top="1235" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0" dealopinion="agree">
    <receivers><![CDATA[54-${P_EMPID}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="POSTDUTY" required="0" locked="1" visible="0"/>
      <field colkey="POSTM2" required="0" locked="1" visible="0"/>
      <field colkey="PARTY" required="0" locked="1" visible="0"/>
      <field colkey="CONENDDATE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="0" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="0" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="0" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="0" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="0" visible="0"/>
      <field colkey="HOSPITALITY" required="1" locked="0" visible="1"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="0" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="56" title="eyIwIjoiSFLlia/mgLvnu4/nkIYifQ==" type="3" level="0" left="13" top="605" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="0" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0" dealopinion="agree">
    <receivers><![CDATA[40-null]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" skipdonenode="0" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="1" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="57" title="eyIwIjoi6LWE5Lqn5aSN5qC4In0=" type="3" level="0" left="75" top="493" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[55-${WF0310.ASSETREVIEWERS}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnprev" title="eyIwIjoi5Zue6YCA5LiK5LiA57qnIn0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="signed" title="eyIwIjoi5Lya562+In0=" comments="1" cssclass="btn-mint" icon="fa fa-group" signedtype="1" signedmode="1" signedperc="0" signedmulti="0"/>
    <action type="signedagree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="signedrefuse" title="eyIwIjoi5ouS57udIn0=" comments="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="1" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="0" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="0" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="0" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="0" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="0" visible="0"/>
      <field colkey="REMARK" required="0" locked="0" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="HROPINION_SJ" required="0" locked="0" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="0" locked="0" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="1" locked="0" visible="1"/>
      <field colkey="AUDITREVIEW" required="0" locked="0" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event>
      <beforesubmit></beforesubmit>
      <beforeback></beforeback>
      <aftersubmit></aftersubmit>
      <afterback></afterback>
      <taskarrive></taskarrive>
      <taskreturn></taskreturn>
      <nodeenter></nodeenter>
      <nodeleave></nodeleave>
    </event>
  </node>
  <node id="58" title="eyIwIjoi56i95qC456a75Lu75a6h6K6hIn0=" type="3" level="0" left="47" top="943" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[19-${WF0310.P_EMPID}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnnode" title="eyIwIjoi5Zue6YCA6IqC54K5In0=" comments="1" skipdonenode="0" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="P_EMPID" required="1" locked="1" visible="0"/>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="1" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISK" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAIN" required="0" locked="1" visible="0"/>
      <field colkey="AUDITDEPARTURE" required="1" locked="0" visible="1"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="ASSETREVIEWERS" required="0" locked="1" visible="0"/>
      <field colkey="ASSETNOTES" required="0" locked="1" visible="0"/>
      <field colkey="ASSETATTACH" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEWTEXT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITREVIEW" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="0" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="0" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="1" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="59" title="eyIwIjoi56i95qC46aOO6Zmp5o6S5p+l57uT5p6c5b2V5YWlIn0=" type="3" level="0" left="237" top="502" nodemode="1" approvemode="1" approveperc="0" approverType="1" skipnouser="1" skiphasdone="0" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" delaycron="" dealauto="0" dealhours="0">
    <receivers><![CDATA[19-${WF0310.P_EMPID}]]></receivers>
    <dealagent><![CDATA[]]></dealagent>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="returnprev" title="eyIwIjoi5Zue6YCA5LiK5LiA57qnIn0=" comments="1" cssclass="btn-lightred" icon="glyphicon glyphicon-backward"/>
    <action type="chart" title="eyIwIjoi5p+l55yL5rWB56iL5Zu+In0=" comments="0" cssclass="btn-info" icon="hricon-flowchart"/>
    <form code="WF0310" canadd="0" candelete="0" locked="0" visible="1" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
      <field colkey="LEAVETYPE" required="0" locked="1" visible="0"/>
      <field colkey="CHANGEREASON" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEREASONDETAIL" required="1" locked="1" visible="0"/>
      <field colkey="YJDISABLEDDATE" required="1" locked="1" visible="0"/>
      <field colkey="CHANGEATTACH" required="0" locked="1" visible="0"/>
      <field colkey="EMAIL_P" required="0" locked="1" visible="0"/>
      <field colkey="MAILNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="DISABLEDDATER" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYSM" required="0" locked="1" visible="0"/>
      <field colkey="SFZFJJBCJ" required="0" locked="1" visible="0"/>
      <field colkey="QRLZYYFL" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANTYPE" required="0" locked="1" visible="0"/>
      <field colkey="LEAVEPLANNOTICETEXT" required="0" locked="1" visible="0"/>
      <field colkey="JJBCJJE" required="0" locked="1" visible="0"/>
      <field colkey="REMARK" required="0" locked="1" visible="0"/>
      <field colkey="DEPOPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="PROJECTRISKOPIN" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGOPINION2" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDIT" required="0" locked="1" visible="0"/>
      <field colkey="HANDOVEREMP" required="0" locked="1" visible="0"/>
      <field colkey="CHECKINGEXPLAINV3" required="1" locked="0" visible="1"/>
      <field colkey="HROPINION_SJ" required="0" locked="1" visible="0"/>
      <field colkey="ISWORKHANDOVER" required="0" locked="1" visible="0"/>
      <field colkey="RETURNCOMPUTER" required="0" locked="1" visible="0"/>
      <field colkey="ORGRELATIONSHIP" required="0" locked="1" visible="0"/>
      <field colkey="THEUNION" required="0" locked="1" visible="0"/>
      <field colkey="EMPCREDENTIALS" required="0" locked="1" visible="0"/>
      <field colkey="ISAUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="FINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="HOSPITALITY" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="AUDITSETTLEMENT_CNS" required="0" locked="1" visible="0"/>
      <field colkey="LABORMANUAL" required="0" locked="1" visible="0"/>
      <field colkey="CERTIFICATE" required="0" locked="1" visible="0"/>
      <field colkey="ISPARENTCHECK" required="0" locked="1" visible="0"/>
      <field colkey="ISDEPCHECK" required="0" locked="1" visible="0"/>
      <field colkey="TRAINSETTLEMENT" required="0" locked="1" visible="0"/>
      <field colkey="TAG1" required="0" locked="1" visible="0"/>
      <field colkey="V1" required="0" locked="1" visible="0"/>
      <field colkey="ATTACH2" required="0" locked="1" visible="0"/>
      <field colkey="V2" required="0" locked="1" visible="0"/>
      <field colkey="V3" required="0" locked="1" visible="0"/>
    </form>
    <form code="WF0321" canadd="0" candelete="0" locked="0" visible="0" imports="0" exports="0">
      <linkcondi><![CDATA[]]></linkcondi>
    </form>
    <event/>
  </node>
  <node id="60" title="eyIwIjoi5rGH6IGa6IqC54K5In0=" type="3" level="0" left="237" top="653" nodemode="1" approvemode="1" approveperc="0" approverType="0" skipnouser="1" skiphasdone="1" skipbeforenode="0" skipreturn="0" applyAgent="1" hideprocess="0" arrivenotify="0" arrivenotifier="" donenotify="0" donenotifier="" delaynotify="0" delayhours="0" dealauto="0" dealhours="0">
    <receivers><![CDATA[]]></receivers>
    <action type="agree" title="eyIwIjoi5ZCM5oSPIn0=" comments="0" cssclass="btn-success" icon="glyphicon glyphicon-ok"/>
    <action type="refuse" title="eyIwIjoi5ouS57udIn0=" comments="1" goon="0" cssclass="btn-warning" icon="glyphicon glyphicon-remove"/>
    <event/>
  </node>
  <transfer snode="5" enode="6" source="Auto" target="Auto" connector="Straight" connstyle="solid">
    <id>5-6</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="6" enode="7" source="Right" target="Left" connector="Flowchart" connstyle="solid">
    <id>6-7</id>
    <title><![CDATA[eyIwIjoi6K6h566X6KGl5YG/6YeRIn0=]]></title>
    <condition><![CDATA[select 1 where ${wf0310.sfzfjjbcj}=1]]></condition>
  </transfer>
  <transfer snode="7" enode="8" source="Bottom" target="Right" connector="Flowchart" connstyle="solid">
    <id>7-8</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="6" enode="8" source="Auto" target="Auto" connector="Straight" connstyle="solid">
    <id>6-8</id>
    <title><![CDATA[]]></title>
    <condition><![CDATA[select 1 where ifnull(${wf0310.sfzfjjbcj},0)=0]]></condition>
  </transfer>
  <transfer snode="10" enode="11" source="Bottom" target="Right" connector="Flowchart" connstyle="solid">
    <id>10-11</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="14" enode="15" source="Auto" target="Auto" connector="Straight" connstyle="solid">
    <id>14-15</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="8" enode="30" source="Bottom" target="Left" connector="Flowchart" connstyle="solid">
    <id>8-30</id>
    <title><![CDATA[eyIwIjoi5ZGY5bel77yM5Z+65bGC77yM5Lit5bGC6Z2e6YOo6Zeo6ZW/In0=]]></title>
    <condition><![CDATA[select 1 where wffn_empFlag(${wf0310.p_empid}) in (1,2)]]></condition>
  </transfer>
  <transfer snode="30" enode="31" source="Bottom" target="Right" connector="Flowchart" connstyle="solid">
    <id>30-31</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="11" enode="32" source="Bottom" target="Right" connector="Flowchart" connstyle="solid">
    <id>11-32</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="32" enode="14" source="Bottom" target="Top" connector="Flowchart" connstyle="solid">
    <id>32-14</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="32" enode="33" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>32-33</id>
    <title><![CDATA[eyIwIjoi5a2Y5Zyo5pyN5Yqh5pyf5YaFIn0=]]></title>
    <condition><![CDATA[select 1 where ${wf0310.p_empid} in (select empid from Eu_TrainningAgreement where ${wf0310.DISABLEDDATE} between startdate and ifnull(enddate,'2099-12-31'))]]></condition>
  </transfer>
  <transfer snode="32" enode="22" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>32-22</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="32" enode="23" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>32-23</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="32" enode="34" source="Bottom" target="Top" connector="Flowchart" connstyle="solid">
    <id>32-34</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="32" enode="24" source="Bottom" target="Top" connector="Flowchart" connstyle="solid">
    <id>32-24</id>
    <title><![CDATA[eyIwIjoi5q2j5byP57yW5Yi2L+WKs+WKoea0vumBoyAmIOS4reWFseWFmuWRmC/kuK3lhbHpooTlpIflhZrlkZgifQ==]]></title>
    <condition><![CDATA[select 1 where (select emptype from md_employee where empid=${wf0310.p_empid}) in (1211,1212) and   (select party from md_employee where empid=${wf0310.p_empid}) in (952,953)]]></condition>
  </transfer>
  <transfer snode="15" enode="36" source="Bottom" target="Top" connector="Flowchart" connstyle="solid">
    <id>15-36</id>
    <title><![CDATA[eyIwIjoi6YOo6Zeo6ZW/In0=]]></title>
    <condition><![CDATA[select 1 where ${wf0310.p_empid} in (select ifnull(emp1,0) from md_organization where orggrade=1012)]]></condition>
  </transfer>
  <transfer snode="33" enode="37" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>33-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="22" enode="37" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>22-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="23" enode="37" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>23-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="34" enode="37" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>34-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="24" enode="37" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>24-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="35" enode="37" source="Bottom" target="Right" connector="Flowchart" connstyle="solid">
    <id>35-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="36" enode="37" source="Bottom" target="Right" connector="Flowchart" connstyle="solid">
    <id>36-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="37" enode="26" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>37-26</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="37" enode="28" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>37-28</id>
    <title><![CDATA[eyIwIjoi5Yqz5Yqh5aSW5YyFL+WunuS5oOeUnyJ9]]></title>
    <condition><![CDATA[select 1 where ${wf0310.empno}  in (select empno from md_employee where emptype in (1221,1214,1213))]]></condition>
  </transfer>
  <transfer snode="26" enode="38" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>26-38</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="28" enode="38" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>28-38</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="38" enode="3" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>38-3</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="1" enode="39" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>1-39</id>
    <title><![CDATA[eyIwIjoi5a6e5Lmg55SfIn0=]]></title>
    <condition><![CDATA[select 1 where ${wf0310.empno}  in (select empno from md_employee where emptype in (1221,1214))]]></condition>
  </transfer>
  <transfer snode="39" enode="40" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>39-40</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="39" enode="41" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>39-41</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="40" enode="42" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>40-42</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="41" enode="42" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>41-42</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="42" enode="37" source="Bottom" target="Right" connector="Flowchart" connstyle="solid">
    <id>42-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="15" enode="43" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>15-43</id>
    <title><![CDATA[eyIwIjoi6Z2e6YOo6Zeo6ZW/LOS4lOebtOaOpeS4iue6p+S4jeS4uumDqOmXqOmVvyJ9]]></title>
    <condition><![CDATA[select 1 from md_employee a ,md_organization b 
where ifnull(a.depid,0)=b.orgid 
and IFNULL(a.ParentID,0)<>ifnull(b.Emp1,0) 
and a.empid=${wf0310.p_empid} 
and ${wf0310.p_empid} not in (select ifnull(emp1,0) from md_organization where orggrade=1012)]]></condition>
  </transfer>
  <transfer snode="43" enode="35" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>43-35</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="15" enode="35" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>15-35</id>
    <title><![CDATA[eyIwIjoi6Z2e6YOo6Zeo6ZW/77yM55u05o6l5LiK57qn5Li66YOo6Zeo6ZW/In0=]]></title>
    <condition><![CDATA[select 1 from md_employee a ,md_organization b 
where ifnull(a.depid,0)=b.orgid 
and IFNULL(a.ParentID,0)=ifnull(b.Emp1,0) 
and a.empid=${wf0310.p_empid} 
and ${wf0310.p_empid} not in (select ifnull(emp1,0) from md_organization where orggrade=1012)]]></condition>
  </transfer>
  <transfer snode="1" enode="44" source="Left" target="Top" connector="Flowchart" connstyle="solid">
    <id>1-44</id>
    <title><![CDATA[eyIwIjoi6Z2e5a6e5Lmg55SfIn0=]]></title>
    <condition><![CDATA[select 1 where ${wf0310.empno} not  in (select empno from md_employee where emptype in (1221,1214))]]></condition>
  </transfer>
  <transfer snode="44" enode="4" source="Bottom" target="Left" connector="Flowchart" connstyle="solid">
    <id>44-4</id>
    <title><![CDATA[eyIwIjoi6Z2e5YmN5Y+w5Lq65ZGYIn0=]]></title>
    <condition><![CDATA[Select case when ${WF0310.isPostType} =1 then 0 else 1 end ;]]></condition>
  </transfer>
  <transfer snode="4" enode="46" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>4-46</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="46" enode="29" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>46-29</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="46" enode="5" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>46-5</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="9" enode="47" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>9-47</id>
    <title><![CDATA[eyIwIjoi5peg56a75Lu75a6h6K6hIn0=]]></title>
    <condition><![CDATA[select case when ifnull(${WF0310.HROpinion_sj},0)=1 then 0 else 1 end ]]></condition>
  </transfer>
  <transfer snode="47" enode="48" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>47-48</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="48" enode="10" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>48-10</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="44" enode="49" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>44-49</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="44" enode="50" source="Bottom" target="Top" connector="Flowchart" connstyle="solid">
    <id>44-50</id>
    <title><![CDATA[eyIwIjoi5YmN5Y+w5Lq65ZGYIn0=]]></title>
    <condition><![CDATA[Select case when ${WF0310.isPostType} =1 then 1 else 0 end ;
]]></condition>
  </transfer>
  <transfer snode="39" enode="51" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>39-51</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="51" enode="42" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>51-42</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="32" enode="52" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>32-52</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="52" enode="37" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>52-37</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="45" enode="4" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>45-4</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="56" enode="9" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>56-9</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="50" enode="57" source="Auto" target="Auto" connector="Straight" connstyle="solid">
    <id>50-57</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="57" enode="45" source="Auto" target="Auto" connector="Straight" connstyle="solid">
    <id>57-45</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="9" enode="58" source="Auto" target="Auto" connector="Straight" connstyle="solid">
    <id>9-58</id>
    <title><![CDATA[eyIwIjoi56a76IGM5a6h6K6hIn0=]]></title>
    <condition><![CDATA[select case when ifnull(${WF0310.HROpinion_sj},0)=1 then 1 else 0 end ]]></condition>
  </transfer>
  <transfer snode="58" enode="47" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>58-47</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="59" enode="56" source="Auto" target="Auto" connector="Straight" connstyle="solid">
    <id>59-56</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="8" enode="60" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>8-60</id>
    <title><![CDATA[eyIwIjoi6YOo6Zeo6ZW/L+mrmOeuoSJ9]]></title>
    <condition><![CDATA[select 1 where wffn_empFlag(${wf0310.p_empid}) in (3,4)]]></condition>
  </transfer>
  <transfer snode="31" enode="60" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>31-60</id>
    <title><![CDATA[]]></title>
  </transfer>
  <transfer snode="60" enode="59" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>60-59</id>
    <title><![CDATA[eyIwIjoi5ZCv5Yqo56i95qC45a6h5p+lIn0=]]></title>
    <condition><![CDATA[select case when ifnull(${WF0310.AuditReview},71164)=71163 then 1 else 0 end]]></condition>
  </transfer>
  <transfer snode="60" enode="56" source="Auto" target="Auto" connector="Flowchart" connstyle="solid">
    <id>60-56</id>
    <title><![CDATA[eyIwIjoi5LiN5ZCv5Yqo56i95qC45a6h5p+lIn0=]]></title>
    <condition><![CDATA[select case when ifnull(${WF0310.AuditReview},71164)=71163 then 0 else 1 end]]></condition>
  </transfer>
  <form type="1" code="WF0310" initnum="1">
    <formscript><![CDATA[hide(leavetype,${WF0310.results} == 1);
hide(disableddate,${WF0310.results} == 1);
hide(QRLZYYFL,${WF0310.results} == 1);
hide(QRLZYYSM,${WF0310.results} == 1);
hide(SFZFJJBCJ,${WF0310.results} == 1);
hide(SFZFJJBCJ,${WF0310.results} == 1);
hide(LEAVEPLANTYPE,${WF0310.results} == 1);
hide(LEAVEPLANNOTICETEXT,${WF0310.results} == 1);
hide(P_EMPID,${WF0310.ISHRBP}==0);
hide(JJBCJJE,${WF0310.SFZFJJBCJ} == 0);
hide(REMARK,${WF0310.SFZFJJBCJ} == 0);
lock(TAG1,${WF0310.P_EMPID} != -1);
hide(isAUDITSETTLEMENT_CNS,${WF0310.AUDITSETTLEMENT_CNS} != 71034);
hide(ORGNAME,${WF0310.P_EMPID} != 0);
hide(depOpinion_sj,${WF0310.V1} == 1);
hide(ProjectRisk,${WF0310.isPostType} == 0||${WF0310.V1} == 1);
hide(ProjectRiskOpin,${WF0310.isPostType} == 0||${WF0310.V1} == 1);
hide(CheckingOpinion,${WF0310.V1} == 1);
hide(CheckingExplain,${WF0310.V1} == 1);
hide(isaudit,${WF0310.V1} == 1);
hide(AuditDeparture,${WF0310.HROPINION_SJ} != 1 || ${WF0310.V1} == 0);
hide(AssetReviewers,${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);
hide(AssetNotes,${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);
hide(AssetAttach,${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);
hide(AssetOverdue,${WF0310.isPostType} == 0|| ${WF0310.V1} == 0);
hide(Hospitality,${WF0310.V1} == 0);
hide(V1,${WF0310.P_EMPID} != 0);
hide(Attach2,${WF0310.V2} == 0 || ${WF0310.V2} == 1);
hide(CHECKINGOPINION2,${WF0310.V2} == 0);
hide(V2,${WF0310.P_EMPID} != 0);
hide(AssetOverdue, ${WF0310.V3} == 1);
hide(CHECKINGEXPLAINV3, ${WF0310.AuditReview} != 71163 &&  ${WF0310.V3} == 1);
hide(ATTACH2,${WF0310.P_EMPID} != 0);]]></formscript>
  </form>
  <form type="1" code="WF0321" initnum="1">
    <initsql><![CDATA[]]></initsql>
    <addnewscript><![CDATA[]]></addnewscript>
    <formscript><![CDATA[hide(HANDOVERATTACH,${WF0321.V2} == 0);]]></formscript>
  </form>
  <mail type="1" file=""/>
  <mail type="2" file=""/>
  <mail type="3" file=""/>
  <mail type="4" file=""/>
  <mail type="5" file=""/>
  <mail type="6" file=""/>
  <mail type="7" file=""/>
  <mail type="8" file=""/>
  <mail type="9" file=""/>
  <mail type="10" file=""/>
  <news type="1"/>
  <news type="2"/>
  <news type="3"/>
  <news type="4"/>
  <news type="5"/>
  <news type="6"/>
  <news type="7"/>
  <news type="8"/>
  <news type="9"/>
  <news type="10"/>
  <event>
    <taskarrive>select 1 as TC;select 1 as TC;select 1 as TC;WFSP_FlowTaskTo_Id(${WF_TASKID},1);WFSP_FlowTaskTo_JSON(${PARAM4},1);api_MH_TASK1(${PARAM5});EP_OARESULT_TO(${PARAM4},1,${PARAM5},${PARAM6});</taskarrive>
    <taskreturn>select 1 as TC;select 1 as TC;select 1 as TC;WFSP_FlowTaskTo_Id(${WF_TASKID},1);WFSP_FlowTaskTo_JSON(${PARAM4},1);api_MH_TASK1(${PARAM5});EP_OARESULT_TO(${PARAM4},1,${PARAM5},${PARAM6});</taskreturn>
    <taskfinish>select 1 as TC;select 1 as TC;select 1 as TC;WFSP_FlowTaskTo_Id(${WF_TASKID},3);WFSP_FlowTaskTo_JSON(${PARAM4},3);api_MH_TASK2(${PARAM5});EP_OARESULT_TO(${PARAM4},2,${PARAM5},${PARAM6});</taskfinish>
    <taskremove>select 1 as TC;select 1 as TC;select 1 as TC;WFSP_FlowTaskTo_Id(${WF_TASKID},1);WFSP_FlowTaskTo_JSON(${PARAM4},1);api_MH_TASK1(${PARAM5});EP_OARESULT_TO(${PARAM4},1,${PARAM5},${PARAM6});</taskremove>
    <noticearrive></noticearrive>
    <noticefinish></noticefinish>
    <noticeremove></noticeremove>
    <flowinitiate></flowinitiate>
    <flowreturn></flowreturn>
    <flowfinish></flowfinish>
  </event>
</flow>