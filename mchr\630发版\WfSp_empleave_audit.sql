create
    definer = rw_clehr@`%` procedure WfSp_empleave_audit(IN p_instanceid int, IN p_type int, OUT p_retval varchar(500))
BEGIN


  DECLARE p_error int DEFAULT 0;
	declare p_id int;
	declare p_emptype int;
	declare p_email varchar(200);
	declare p_empid int;


  DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET p_error = 1;

  SET p_retval = 0;
  SET autocommit = 0;

	select a.emptype,a.p_empid into p_emptype ,p_empid
	from wf_ht_empchange a  where a.wfinstanceid=p_instanceid;

		if p_type=1 then

	select id,email_p into p_id,p_email
	from eu_empchange
	where ifnull(wfinstanceid,0)=p_instanceid;

	call ep_empdo_email(p_id,null,17,p_retval);

	end if ;

		COMMIT;
		SET autocommit = 1;


END;


