describe:
  ## 项目级别服务定义
  ## 应用名称(必填)
  name: "${PROJECT_NAME}"
  ## 用来说明当前服务在整个系统中可能扮演的不同角色。比如 前端应用使用 frontend，后端应用使用backend。
  component: "backend"
  ## 用来说明当前服务属于哪个程序。
  partOf: ""
  ## 用来说明当前服务在整个业务系统中所属的域
  domain: ""
  ## 是当前服务上级域的名字
  subdomainOf: ""
  
  ## 公司级别系统定义
  ## 系统类型 （基础设施）
  system_type: "jygl"
  ## 系统 （汉得1.11底座）
  system: "cloa-bpm"
  ## 子系统 （汉得1.11底座）
  subsystem: "cloa-bpm"
  ## 模块 （汉得1.11底座）
  module: "bpm-form"
  ## 科室 （架构运维室）
  office: ""
  ## 团队 （架构运维室）
  team: ""
  ## 负责人
  admin: "wangweikang"
  
## replicaCount指副本数量，就是指要启动多少个pod。默认为1个pod，即一个副本
replicaCount: 1

## env是环境变量配置，是根据SpringBoot配置文件application.yml bootstrap.yml 使用的${}获取的环境变量值，需要根据每个环境设置不同值，则在这里配置
## 可以根据自身项目情况进行增减，不需要的可以删除
env:
  # NACOS 地址
  NACOS_SERVER: bpm-nacos-cs:8848
  # NACOS NAMESPACE
  NACOS_NAMESPACE: dev
  LOG_LEVEL: info
  NACOS_USER: nacos
  NACOS_PASSWORD: seFLZ1@25YcDFw
  # 日志级别
  LOG_LEVEL: info
  JAVA_OPTS: |
    -Xms1024m -Xmx1536m


serverPorts:
  ## httpPort 是其中一个服务端口的名称，下一级定义了端口的其他信息
  httpPort:
    ## protocol 是httpPort这个端口的协议
    ## 支持的协议：SCTP, TCP(默认值), UDP
    protocol: TCP
    ## port 是httpPort这个端口的端口号
    port: 8050
    ## service 指是否通过service将此端口(httpPort)公开到网络中。只支持创建一个类型是clusterIP的service, 名称是 {describe.name}
    ## service是什么？In Kubernetes, a Service is a method for exposing a network application that is running as one or more Pods in your cluster.
    ## 关于service: https://kubernetes.io/docs/concepts/services-networking/service/
    service: true
  ## actuator 暴露的管理端口，用于健康检查和配置管理，下一级定义了端口的其他信息
  managePort:
    ## protocol 是managePort这个端口的协议
    protocol: TCP
    port: 8051
    ## service 指是否通过service将此端口(managePort)公开到网络中,managePort建议不通过service暴露到集群外网络
    service: false

## 监控相关路径
metrics:
  ## prometheus路径
  prometheusPath: /actuator/prometheus
  ## 健康检查路径
  healthPath: /actuator/health   

# 资源限制
resources:
  limits:
    cpu: "1"
    memory: 2Gi
  requests:
    cpu: 100m
    memory: 1Gi
    
## 如需要关闭健康检查请放开以下注释
probe:
  livenessProbe:
    enabled: false
  readinessProbe:
    enabled: false