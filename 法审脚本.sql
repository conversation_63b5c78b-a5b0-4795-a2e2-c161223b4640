SELECT * from HPTL_TODO_MID a where a.RECEIVER in ('linxh', 'xuchao','zhang<PERSON><PERSON>ao','zhaoyijing') and a.CREATION_DATE between to_date
                ('2024-01-01 00:00:00','yyyy-mm-dd hh24:mi:ss') 
        and to_date('2024-12-16 07:00:00','yyyy-mm-dd hh24:mi:ss') and a.DATA_TYPE = 'TODO' and a.SYSTEM_CODE = 'portal-it-service'




SELECT 
    he.name AS employee_name, 
    hu.unit_name AS unit_name
FROM 
    hpfm_employee_assign hea
JOIN 
    hpfm_employee he ON hea.employee_id = he.employee_id
JOIN 
    hpfm_unit hu ON hea.unit_id = hu.unit_id;


SELECT * from HPTL_TODO_MID a where a.RECEIVER in ('xumy', 'hansh','wangyong','chenchen') and a.CREATION_DATE between to_date
                ('2024-08-01 00:00:00','yyyy-mm-dd hh24:mi:ss') 
        and to_date('2025-01-31 23:00:00','yyyy-mm-dd hh24:mi:ss') and a.DATA_TYPE = 'TODO' and a.SYSTEM_CODE = 'portal-it-service' and title LIKE '供应商' 
AND title LIKE '采购';

