# FROM harbor-c7n.csleasing.com.cn/choerodon/oraclejdk:v1.8
FROM harbor-c7n.csleasing.com.cn/basekylin/javabase:1.8.0.442

COPY uc.jar /uc.jar
# COPY ./start.sh /start.sh
EXPOSE 8087
# RUN chmod 777 /start.sh
# RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo ‘Asia/Shanghai’ >/etc/timezone
# ENTRYPOINT ["/start.sh"]

CMD exec java -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap $JAVA_OPTS -jar /uc.jar