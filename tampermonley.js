// ==UserScript==
// @name         New Userscript
// @namespace    http://tampermonkey.net/
// @version      2025-03-19
// @description  try to take over the world!
// <AUTHOR>
// @match        https://bbs.hupu.com/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    // 将topmeue 原本红色褪色
   document.querySelectorAll('.hp-pc-rc-TopMenu, .hp-pc-rc-menu-banner')
    .forEach(el => el.style.setProperty('background', 'none', 'important'));
    // 删除右侧游戏中心广告
    document.getElementById('game-center-entrance-container')?.remove();
    // 把图片缩小
    document.querySelectorAll('.image-wrapper').forEach(el => {
    el.style.width = '20px';
    el.style.height = '20px';
    el.style.overflow = 'hidden';
    });
})();

// --------------------------------------

// ==UserScript==
// @name         New Userscript
// @namespace    http://tampermonkey.net/
// @version      2025-03-19
// @description  try to take over the world!
// <AUTHOR>
// @match        https://sqlopr-int.csleasing.com.cn/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=csleasing.com.cn
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
document.querySelectorAll('.h-100.overflow-hidden.unselect').forEach(el => {
    el.classList.remove('unselect');
});
function renameUnselectToCosco(element) {
    // 找到当前元素下所有 class 为 'unselect' 的子元素
    element.querySelectorAll('.unselect').forEach(el => {
        el.classList.replace('unselect', 'cosco');
    });

    // 递归遍历所有子元素
    element.querySelectorAll('*').forEach(child => {
        renameUnselectToCosco(child);
    });
}

// 获取 class 为 modal-content 的元素
const modalContent = document.querySelector('.modal-content');
if (modalContent) {
    renameUnselectToCosco(modalContent);
}
    // Your code here...
})();