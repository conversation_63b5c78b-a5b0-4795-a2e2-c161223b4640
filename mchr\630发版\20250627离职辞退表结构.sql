﻿-- 
-- Script was generated by Devart dbForge Studio for MySQL, Version *********
-- Product Home Page: http://www.devart.com/dbforge/mysql/studio
-- Script date 2025/6/27 15:09:41
-- Source server version: 8.0.28
-- Source connection string: User Id=rw_clehr;Host=*************;Port=3307;Database=clehr;Character Set=utf8
-- Target server version: 8.0.39
-- Target connection string: User Id=rw_clehr;Host=*************;Port=3308;Character Set=utf8
-- Run this script against clehr to synchronize it with clehr
-- 


--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;


--
-- Set default database
--
USE clehr;
ALTER TABLE wf_ht_empchange ADD COLUMN AuditReviewText varchar(500) null COMMENT '资产复核意见';

ALTER TABLE eu_assetreviewersADD COLUMN fgempid DECIMAL NULL;
--
-- Alter column `Attach2` on table `wf_ht_empchange`
--
ALTER TABLE wf_ht_empchange 
  CHANGE COLUMN Attach2 Attach2 VARCHAR(500) DEFAULT NULL;

--
-- Alter column `V2` on table `wf_ht_empchange`
--
ALTER TABLE wf_ht_empchange 
  CHANGE COLUMN V2 V2 INT UNSIGNED DEFAULT NULL;

--
-- Create column `V3` on table `wf_ht_empchange`
--
ALTER TABLE wf_ht_empchange 
  ADD COLUMN V3 INT DEFAULT NULL;

--
-- Create column `IsWorkHandoverV3` on table `wf_ht_empchange`
--
ALTER TABLE wf_ht_empchange 
  ADD COLUMN IsWorkHandoverV3 INT DEFAULT NULL;

--
-- Create column `CheckingOpinion3` on table `wf_ht_empchange`
--
ALTER TABLE wf_ht_empchange 
  ADD COLUMN CheckingOpinion3 INT DEFAULT NULL;

--
-- Create column `CheckingExplainV3` on table `wf_ht_empchange`
--
ALTER TABLE wf_ht_empchange 
  ADD COLUMN CheckingExplainV3 VARCHAR(500) DEFAULT NULL;

--
-- Change columns order on table `wf_ht_empchange`
--
ALTER TABLE wf_ht_empchange 
 MODIFY AuditReviewText VARCHAR(500) DEFAULT NULL COMMENT '资产复核意见' AFTER AuditReview;
ALTER TABLE wf_ht_empchange 
 MODIFY V2 INT UNSIGNED DEFAULT NULL AFTER Attach2;
ALTER TABLE wf_ht_empchange 
 MODIFY CheckingOpinion2 INT DEFAULT NULL AFTER V2;
