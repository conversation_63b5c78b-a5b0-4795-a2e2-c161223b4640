describe:
  name: "clmp-portal"
  component: "backend"
  partOf: ""
  domain: ""
  subdomainOf: ""
  
  system_type: "jygl"
  system: "cloa-portal"
  subsystem: "clmp-portal"
  module: "clmp-portal"
  office: ""
  team: ""
  ## 负责人
  admin: "wangweikang"

replicaCount: 1

env:
  EUREKA_DEFAULT_ZONE: '*********************************************/eureka/'
  JAVA_OPTS: -Xms1024m -Xmx2048m -XX:MaxRAMFraction=1
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: com.oceanbase.jdbc.Driver
  SPRING_DATASOURCE_PASSWORD: Devgch#123clmp
  SPRING_DATASOURCE_URL: '********************************************************************'
  SPRING_DATASOURCE_USERNAME: clmp@hfbcoracle#cosco
  SPRING_REDIS_DATABASE: '1'
  SPRING_REDIS_HOST: *************
  SPRING_REDIS_PASSWORD: L3tMeIn!H@rd
  SPRING_REDIS_PORT: '6379'
  LOG_LEVEL: DEBUG
  # 工作流agentId
  QY_WECHAT_WORKFLOW_AGENT_ID: 1000149
  # 工作流应用Secret
  QY_WECHAT_SCHEDULE_CORPSECRT: wt-eZMEyaKdgANTIyH2jylcZbAd7iFcz4Avr4mKZGLo
  # 企业ID
  QY_WECHAT_CORPID: ww188c2ab8f2cfc1e7

serverPorts:
  httpPort:
    protocol: TCP
    port: 8200
    service: false
  managePort:
    protocol: TCP
    port: 8201
    service: false
    
## 监控相关路径
metrics:
  prometheusPath: /actuator/prometheus
  healthPath: /actuator/health   

# 资源限制
resources:
  limits:
    cpu: "1"
    memory: 2Gi
  requests:
    cpu: 100m
    memory: 1Gi