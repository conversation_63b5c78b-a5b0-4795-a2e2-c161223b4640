select
    `a`.`EmpNo` AS `EMP_CODE`,
    regexp_replace (`a`.`Title`, '[0-9]', '') AS `EMP_NAME`,
    'False' AS `IS_PRIMARY_POST`,
    ifnull (`b`.`outerCode`, `b`.`PostCode`) AS `POST_CODE`,
    `b`.`Title` AS `POST_NAME`,
    NULL AS `LEADER_POST_CODE`,
    NULL AS `LEADER_POST_NAME`,
    ifnull (`d`.`old_orgid`, `d`.`OrgID`) AS `ONE_ORG_ID`,
    `d`.`orgcode` AS `ONE_ORG_CODE`,
    `d`.`Title` AS `ONE_ORG_NAME`,
    ifnull (`c`.`old_orgid`, `c`.`OrgID`) AS `ORG_ID`,
    `c`.`orgcode` AS `ORG_CODE`,
    `c`.`Title` AS `ORG_NAME`,
    ifnull (`e`.`old_orgid`, `e`.`OrgID`) AS `LEADER_ORG_ID`,
    `e`.`orgcode` AS `LEADER_ORG_CODE`,
    `e`.`Title` AS `LEADER_ORG_NAME`,
    (
        case
            when (`a`.`Disabled` = 1) then 'True'
            else 'False'
        end
    ) AS `IS_ENABLE`,
    date_format (`a`.`Begindate`, '%Y/%m/%d %H:%i:%S') AS `HR_CREATE_DATE`,
    date_format (`a`.`Begindate`, '%Y/%m/%d %H:%i:%S') AS `HR_LAST_DATE`
from
    (
        (
            (
                (
                    `clehr`.`eu_parttime` `a`
                    left join `clehr`.`md_position` `b` on ((`a`.`PTPostID` = `b`.`PostID`))
                )
                left join `clehr`.`md_organization` `c` on ((`a`.`PTOrgID` = `c`.`OrgID`))
            )
            left join `clehr`.`md_organization` `d` on ((`ofn_OrgDepid` (`a`.`PTOrgID`) = `d`.`OrgID`))
        )
        left join `clehr`.`md_organization` `e` on ((`c`.`ParentID` = `e`.`OrgID`))
    )
union
select
    `a`.`EmpNo` AS `EMP_CODE`,
    regexp_replace (`a`.`Title`, '[0-9]', '') AS `EMP_NAME`,
    'True' AS `IS_PRIMARY_POST`,
    ifnull (`b`.`outerCode`, `b`.`PostCode`) AS `POST_CODE`,
    `b`.`Title` AS `post_name`,
    NULL AS `LEADER_POST_CODE`,
    NULL AS `LEADER_POST_NAME`,
    ifnull (`d`.`old_orgid`, `d`.`OrgID`) AS `ONE_ORG_ID`,
    `d`.`orgcode` AS `ONE_ORG_CODE`,
    `d`.`Title` AS `ONE_ORG_NAME`,
    ifnull (`c`.`old_orgid`, `c`.`OrgID`) AS `ORG_ID`,
    `c`.`orgcode` AS `ORG_CODE`,
    `c`.`Title` AS `ORG_NAME`,
    ifnull (`e`.`old_orgid`, `e`.`OrgID`) AS `LEADER_ORG_ID`,
    `e`.`orgcode` AS `LEADER_ORG_CODE`,
    `e`.`Title` AS `LEADER_ORG_NAME`,
    (
        case
            when (`a`.`Disabled` = 1) then 'True'
            else 'False'
        end
    ) AS `IS_ENBLE`,
    date_format (`a`.`CreateDate`, '%Y/%m/%d %H:%i:%S') AS `HR_CREATE_DATE`,
    date_format (`clehr`.`f`.`begindate`, '%Y/%m/%d %H:%i:%S') AS `HR_LAST_DATE`
from
    (
        (
            (
                (
                    (
                        `clehr`.`md_employee` `a`
                        left join `clehr`.`md_position` `b` on ((`a`.`PostID` = `b`.`PostID`))
                    )
                    left join `clehr`.`md_organization` `c` on ((`a`.`OrgID` = `c`.`OrgID`))
                )
                left join `clehr`.`md_organization` `d` on ((`ofn_OrgDepid` (`a`.`OrgID`) = `d`.`OrgID`))
            )
            left join `clehr`.`md_organization` `e` on ((`c`.`ParentID` = `e`.`OrgID`))
        )
        left join `clehr`.`mv_emp_event` `f` on ((`a`.`EmpID` = `clehr`.`f`.`EmpID`))
    )