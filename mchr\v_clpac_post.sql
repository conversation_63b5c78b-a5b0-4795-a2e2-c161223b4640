select
    ifnull (`a`.`outerCode`, `a`.`PostCode`) AS `POST_CODE`,
    `a`.`Title` AS `POST_NAME`,
    ifnull (`b`.`old_orgid`, `b`.`OrgID`) AS `ORG_ID`,
    `b`.`orgcode` AS `ORG_CODE`,
    `b`.`Title` AS `ORG_NAME`,
    'Company1' AS `COMPANY_CODE`,
    (
        case
            when (`a`.`Disabled` = 1) then 'True'
            else 'False'
        end
    ) AS `IS_ENABLE`,
    date_format (`a`.`CreateDate`, '%Y/%m/%d %H:%i:%S') AS `HR_CREATE_DATE`,
    date_format (`a`.`DisabledDate`, '%Y/%m/%d %H:%i:%S') AS `END_DATE`,
    ifnull (
        date_format (`c`.`ClosedTime`, '%Y/%m/%d %H:%i:%S'),
        date_format (`a`.`CreateDate`, '%Y/%m/%d %H:%i:%S')
    ) AS `HR_LAST_DATE`,
    NULL AS `HR_CODE`,
    NULL AS `LEADER_POST_CODE`,
    NULL AS `LEADER_POST_NAME`,
    NULL AS `LEADER_ORG_CODE`,
    NULL AS `LEADER_ORG_NAME`,
    `c`.`changedate` AS `changedate`
from
    (
        (
            `clehr`.`md_position` `a`
            left join `clehr`.`md_organization` `b` on ((`a`.`OrgID` = `b`.`OrgID`))
        )
        left join (
            select
                row_number() OVER (
                    PARTITION BY
                        `a`.`PostID`
                    ORDER BY
                        `a`.`ClosedTime` desc
                ) AS `rn`,
                `a`.`PostID` AS `postid`,
                `a`.`changedate` AS `changedate`,
                `a`.`ClosedTime` AS `ClosedTime`
            from
                `clehr`.`ou_postchange` `a`
            where
                (ifnull (`a`.`Closed`, 0) = 1)
        ) `c` on ((`a`.`PostID` = `c`.`postid`))
    )
where
    (`c`.`rn` = 1)