--- 绩效

SELECT DISTINCT
    p.id             AS permission_id,
    p.name           AS permission_name,
    p.url,
    p.hidden,
    p.del_flag,
    r.id             AS role_id,
    r.role_name           AS role_name,
    u.id             AS user_id,
    u.username
FROM sys_permission p
LEFT JOIN sys_role_permission rp ON p.id = rp.permission_id
LEFT JOIN sys_role r ON rp.role_id = r.id
LEFT JOIN sys_user_role ur ON r.id = ur.role_id
LEFT JOIN sys_user u ON ur.user_id = u.id
WHERE (
    -- 权限有用户关联
    u.id IS NOT NULL

    -- 或者是特殊的 online 权限
    OR (p.url LIKE '%:code' AND p.url LIKE '/online%' AND p.hidden = 1)
    OR p.url = '/online'
)
AND p.del_flag = 0;

---采购

SELECT 
  u.id AS user_id,
  u.login_name,
  u.real_name,
  r.id AS role_id,
  r.name AS role_name
FROM 
  iam_role r
JOIN 
  iam_member_role mr ON r.id = mr.role_id
JOIN 
  iam_user u ON mr.member_id = u.id
WHERE 
  r.name LIKE '%采购%';

iam_role IAM_PERMISSION IAM_ROLE_PERMISSION IAM_MENU IAM_MENU_PERMISSION
 
---用印
SELECT 
  u.id AS user_id,
  u.login_name,
  u.real_name,
  r.id AS role_id,
  r.name AS role_name
FROM 
  iam_role r
JOIN 
  iam_member_role mr ON r.id = mr.role_id
JOIN 
  iam_user u ON mr.member_id = u.id
WHERE 
  r.name LIKE '%用印%';


--- 门户
SELECT 
  r.id AS role_id,
  r.name AS role_name,
  u.id AS user_id,
  u.login_name,
  u.real_name
FROM 
  iam_role r
JOIN 
  iam_member_role mr ON r.id = mr.role_id
JOIN 
  iam_user u ON mr.member_id = u.id;


---鲸到

SELECT 
  u.id AS user_id,
  u.login_name,
  u.real_name,
  r.id AS role_id,
  r.name AS role_name
FROM 
  iam_role r
JOIN 
  iam_member_role mr ON r.id = mr.role_id
JOIN 
  iam_user u ON mr.member_id = u.id
WHERE 
  r.name LIKE '%考勤%';

---鲸彩

SELECT 
  u.id AS user_id,
  u.login_name,
  u.real_name,
  r.id AS role_id,
  r.name AS role_name
FROM 
  iam_role r
JOIN 
  iam_member_role mr ON r.id = mr.role_id
JOIN 
  iam_user u ON mr.member_id = u.id
WHERE 
  r.name LIKE '%团队%';
 


