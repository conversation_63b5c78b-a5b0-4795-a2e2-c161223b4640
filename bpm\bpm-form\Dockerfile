# FROM harbor-c7n.csleasing.com.cn/choerodon/oraclejdk:v1.8-centos7.6-tomcat8.5.7-zh_CN
# FROM harbor-c7n.csleasing.com.cn/kylin/javabase:oraclejdk-8u202
FROM harbor-c7n.csleasing.com.cn/basekylin/javabase:1.8.0.442

# RUN /bin/sh -c 'mkdir -p /home/<USER>/etc/machine-id && rm -f /var/lib/dbus/machine-id && systemd-machine-id-setup'
ENV JAVA_TOOL_OPTIONS -Dfile.encoding=UTF-8
ENV LANG C.UTF-8

COPY form.jar /form.jar
# COPY ./start.sh /start.sh
EXPOSE 8087

# ENTRYPOINT ["sh", "-c", "java -Xms1024m -Xmx4096m -jar /home/<USER>/form/form.jar --spring.config.location=classpath:/config/application.yml,/home/<USER>/form/application-uat.yml", "&"]
# RUN chmod 777 /start.sh
# ENTRYPOINT ["/start.sh"]
CMD exec java -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap $JAVA_OPTS -jar /form.jar