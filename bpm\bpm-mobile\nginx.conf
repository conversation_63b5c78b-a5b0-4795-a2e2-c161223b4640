user  nginx;
worker_processes  4;
events {
    worker_connections  1024;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] '
                     'fwf[$http_x_forwarded_for] tip[$http_true_client_ip] '
                     '$upstream_addr $upstream_response_time $request_time '
                     '$http_host $request '
                     '"$status" $body_bytes_sent "$http_referer" '
                     '"$http_accept_language" "$http_user_agent" ';
    sendfile        on;
    #tcp_nopush     on;
    #keepalive_timeout  0;
    keepalive_timeout  65;
    #gzip  on;
    upstream form_app {
       server form_app_ip:form_app_port;
    }
    upstream portal_app {
       server portal_app_ip:portal_app_port;
    }
    upstream runtime_app {
       server runtime_app_ip:runtime_app_port;
    }
    upstream bpmModel_app {
       server bpmModel_app_ip:bpmModel_app_port;
    }
    upstream uc_app {
       server uc_app_ip:uc_app_port;
    }

    server {
        listen       80;
        access_log  /opt/log/nginx/access.log;
        error_log   /opt/log/nginx/access.log;
        
        location ^~ /form/ {
            proxy_pass              http://form_app/;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ^~ /portal/ {
            proxy_pass              http://portal_app/;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ^~ /bpmRunTime/ {
            proxy_pass              http://runtime_app/;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ^~ /bpmModel/ {
            proxy_pass              http://bpmModel_app/;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ^~ /uc/ {
            proxy_pass              http://uc_app/;
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location /mobilevue/ {
          alias /opt/web/ui/mobilevue/;
          try_files  $uri $uri/ /index.html;
        }

        location / {
            root       /opt/web/ui/mobilevue;
            index      index.html index.htm;
            try_files  $uri $uri/ /index.html;
        }

#        location @router{
#            rewrite ^.*$ /index.html?s=$1 last;
#        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
    proxy_connect_timeout 5;
    proxy_read_timeout 60;
    proxy_send_timeout 5;
    proxy_buffer_size 16k;
    proxy_buffers 4 64k;
    client_max_body_size 50M;
    proxy_busy_buffers_size 128k;
    proxy_temp_file_write_size 128k;
}

