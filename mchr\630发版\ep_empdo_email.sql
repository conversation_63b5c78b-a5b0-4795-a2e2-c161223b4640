create
    definer = rw_clehr@`%` procedure ep_empdo_email(IN p_ID varchar(30), IN P_USERID int, IN P_XTYPE int, OUT p_retval int)
BEGIN
    DECLARE P_MailTo VARCHAR(1000);
		DECLARE P_MailCc VARCHAR(2000);
		DECLARE P_Subject VARCHAR(2000);
		DECLARE P_Body text;
		DECLARE P_Attachment VARCHAR(5000);
		DECLARE p_title VARCHAR(500);
		DECLARE p_date VARCHAR(500);
		DECLARE p_oldorgid int;
		DECLARE p_orgid int;
		DECLARE p_depid int;
		DECLARE p_compid int;		
		DECLARE p_emailp VARCHAR(200);
		DECLARE p_mobileno VARCHAR(200);
		DECLARE p_spName VARCHAR(200);
		DECLARE p_SpChange VARCHAR(200);
		DECLARE p_empno VARCHAR(200);
		DECLARE p_EmpType VARCHAR(200);
		DECLARE p_oldorgpath VARCHAR(200);
		DECLARE p_orgpath VARCHAR(200);
		DECLARE p_oldpostname VARCHAR(200);
		DECLARE p_postname VARCHAR(200);
		DECLARE P_Createdate VARCHAR(200);
		DECLARE P_changedate VARCHAR(200);
		DECLARE p_lastworkdate VARCHAR(200);
		DECLARE p_workcity VARCHAR(200);
		DECLARE p_postcustom6 VARCHAR(200);
		DECLARE P_JoinType VARCHAR(200);	
	  DECLARE p_parentid VARCHAR(200);	
	  DECLARE p_ChangeType int;		
		-- DECLARE p_SpChange VARCHAR(200);		
DECLARE p_error int DEFAULT 0;

DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET p_error = 1;

SET p_retval = 0;

CREATE TEMPORARY TABLE IF NOT EXISTS tmp_Email(
        ID int AUTO_INCREMENT PRIMARY KEY,
        Email varchar(100)
      );
-- 发送入职信息邮件
    IF P_XTYPE = 1 THEN
			-- 插入发送邮件
			set p_date=CONCAT(YEAR(NOW()),'年',MONTH(NOW()),'月',DAY(NOW()),'日');
			select OrgID,Depid,ComPany into p_orgid,p_depid,p_compid from eu_empchange where id=P_ID;
			
		  select a.empno,a.title,CONCAT(date_format(a.Createdate,'%Y-%m-%d'),oFN_week(a.Createdate)),
--       case when ifnull(EmpCustom6,0)=70161 then '非集中供餐' else '集中供餐' END  20240328
      b.Title AS EmpCustom6
       into p_empno,p_title,P_Createdate,p_postcustom6 
      from eu_empchange a LEFT JOIN md_codes b ON ifnull(a.EmpCustom6,0)=b.id
      where a.id=P_ID;	
	
     	-- 主送对象
			select concat(ofn_empemail(a.orgid),';',b.email) into P_MailTo 
			from md_organization a left join md_employee b on b.empid=a.Emp3
			where a.orgid=p_orgid; 	
			
			insert into tmp_Email(Email)
			select SUBSTRING_INDEX(SUBSTRING_INDEX(P_MailTo,';',b.help_topic_id+1),';',-1) as Email
      from mysql.help_topic b
      where b.help_topic_id<(Length(P_MailTo)-Length(REPLACE(P_MailTo,';',''))+1);
	
			select GROUP_CONCAT(DISTINCT a.email order by c.ORDERBY asc SEPARATOR ';') into P_MailTo
			from tmp_Email a left join md_employee b on a.email=b.email
											 left join md_codes c on b.postm2=c.id;		
				
		  drop table tmp_Email;		
			
			select title into p_EmpType from md_codes where id in(select emptype from  eu_empchange where id=P_ID);	
			-- 20221111 新加字段 入职形式
		  select title into P_JoinType from md_codes where id in(select JoinType from  eu_empchange where id=P_ID);
			
			select oFN_Findsite(id) into p_workcity from md_address where id in(select WORKCITY from eu_empchange where id=P_ID);
			
	    select GROUP_CONCAT(title SEPARATOR '>') into p_orgpath from md_organization where find_in_set(orgid,oFN_FindOrgLine(p_orgid,2,1));
			
      select title into p_postname from md_position where postid in(select postid from eu_empchange where id=P_ID);
			
			-- select title into p_postcustom6 from md_codes where id in(select POSTCUSTOM5 from eu_pre_empinfo where APPLYID=P_APPLYID);
			
			/*
		  select REPLACE(MailText,'【员工编号】',p_empno) into p_body
						 from mc_mailtemplate WHERE id=4;
			select REPLACE(p_body,'【员工姓名】',p_title) into p_body;
		  select REPLACE(p_body,'【用工类型】',p_emptype) into p_body;
			select REPLACE(p_body,'【组织路径】',p_orgpath) into p_body;	
			select REPLACE(p_body,'【岗位名称】',p_postname) into p_body;
			select REPLACE(p_body,'【入职日期】',P_Createdate) into p_body;	*/	 
select mailtitle,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(MailText,'【供餐形式】',ifnull(p_postcustom6,'')),'【员工编号】',ifnull(p_empno,'')),'【员工姓名】',ifnull(p_title,'')),'【用工形式】',ifnull(p_emptype,'')),'【组织路径】',ifnull(p_orgpath,'')),'【岗位名称】',ifnull(p_postname,'')),'【入职日期】',P_Createdate),'【日期】',p_date),'【入职形式】',ifnull(P_JoinType,'')),'【工作城市】',ifnull(p_workcity,'')),MailAttach 
		   into p_subject,p_body,P_Attachment
			 from mc_mailtemplate WHERE id=12;
			-- 抄送对象			 
			select email4 into P_MailCc from ep_email;
			
      insert into mc_mailitem(ConfigID,MailTo,MailCc,`SUBJECT`,Body,Attachment)
			select 2,P_MailTo,p_mailCc,p_subject,p_body,p_attachment;

    END IF;	
		
		-- 发送调动邮件
		IF P_XTYPE = 3 THEN
		 
		  set p_ChangeType=(select ChangeType from eu_empchange where id=p_id);
		  -- 调动通知邮件
			IF p_ChangeType=60099 THEN
			
			-- 插入发送邮件
			set p_date=CONCAT(YEAR(NOW()),'年',MONTH(NOW()),'月',DAY(NOW()),'日');
			select OrgID,Old_Orgid,CONCAT(date_format(changedate,'%Y-%m-%d'),oFN_week(changedate)) into p_orgid,p_oldorgid,p_changedate 
			from eu_empchange where id=P_ID;
		  select empno,title into p_empno,p_title from eu_empchange where id=P_ID;
		
	    -- 主送对象
			select CONCAT(email1,';',email2) into P_MailTo from ep_email;	
			
			select title into p_EmpType from md_codes where id in(select emptype from md_employee where empid=(select empid from eu_empchange where id=P_ID));	
			
			select GROUP_CONCAT(title SEPARATOR '>') into p_oldorgpath from md_organization where find_in_set(orgid,oFN_FindOrgLine(p_oldorgid,2,1));
				
	    select GROUP_CONCAT(title SEPARATOR '>') into p_orgpath from md_organization where find_in_set(orgid,oFN_FindOrgLine(p_orgid,2,1));
			
			select title into p_oldpostname from md_position where postid in(select Old_Postid from eu_empchange where id=P_ID);
			
      select title into p_postname from md_position where postid in(select postid from eu_empchange where id=P_ID);
			
			select title into p_parentid from md_employee where empid in(select parentid from eu_empchange where id=P_ID);
			
			-- select title into p_postcustom6 from md_codes where id in(select POSTCUSTOM5 from eu_pre_empinfo where APPLYID=P_APPLYID);
			
			/*
		  select REPLACE(MailText,'【员工编号】',p_empno) into p_body
						 from mc_mailtemplate WHERE id=4;
			select REPLACE(p_body,'【员工姓名】',p_title) into p_body;
		  select REPLACE(p_body,'【用工类型】',p_emptype) into p_body;
			select REPLACE(p_body,'【组织路径】',p_orgpath) into p_body;	
			select REPLACE(p_body,'【岗位名称】',p_postname) into p_body;
			select REPLACE(p_body,'【入职日期】',P_Createdate) into p_body;	*/	 
		select mailtitle,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(MailText,'【员工编号】',ifnull(p_empno,'')),'【员工姓名】',ifnull(p_title,'')),'【用工类型】',ifnull(p_emptype,'')),'【原部门】',ifnull(p_oldorgpath,'')),'【原岗位】',ifnull(p_oldpostname,'')),'【新组织】',ifnull(p_orgpath,'')),'【日期】',p_date),'【新岗位】',ifnull(p_postname,'')),'【直属主管】',ifnull(p_parentid,'')),'【调动日期】',ifnull(p_changedate,'')),MailAttach 
		   into p_subject,p_body,P_Attachment
			 from mc_mailtemplate WHERE id=15;
			 
			 -- 抄送对象		 
			select ofn_empemail(orgid) into P_MailCc from md_organization where orgid=p_oldorgid;
			
			select CONCAT(P_MailCc,';',ofn_empemail(orgid)) into P_MailCc from md_organization where orgid=p_orgid;
			
			insert into tmp_Email(Email)
			select SUBSTRING_INDEX(SUBSTRING_INDEX(P_MailCc,';',b.help_topic_id+1),';',-1) as Email
      from mysql.help_topic b
      where b.help_topic_id<(Length(P_MailCc)-Length(REPLACE(P_MailCc,';',''))+1);	
		
			select GROUP_CONCAT(DISTINCT a.email order by c.ORDERBY,a.id asc SEPARATOR ';') into P_MailCc
			from tmp_Email a left join md_employee b on a.email=b.email
											 left join md_codes c on b.postm2=c.id
			where a.email <>(select email from md_employee where empno=p_empno);  #by lynx 不抄送本人
			
			
			drop table tmp_Email;		
			
      insert into mc_mailitem(ConfigID,MailTo,MailCc,`SUBJECT`,Body,Attachment)
			select 2,p_mailto,p_mailCc,p_subject,p_body,p_attachment;
			
			-- select p_postname;
			
			END IF;
			
			-- 换岗通知邮件
			IF p_ChangeType=60098 THEN
			
			-- 插入发送邮件
			set p_date=CONCAT(YEAR(NOW()),'年',MONTH(NOW()),'月',DAY(NOW()),'日');
			select OrgID,Old_Orgid,CONCAT(date_format(changedate,'%Y-%m-%d'),oFN_week(changedate)) into p_orgid,p_oldorgid,p_changedate 
			from eu_empchange where id=P_ID;
		  select empno,title into p_empno,p_title from eu_empchange where id=P_ID;
		
	    -- 主送对象
			select CONCAT(email1,';',email2) into P_MailTo from ep_email;	
			
			select title into p_EmpType from md_codes where id in(select emptype from md_employee where empid=(select empid from eu_empchange where id=P_ID));	
			
			select GROUP_CONCAT(title SEPARATOR '>') into p_oldorgpath from md_organization where find_in_set(orgid,oFN_FindOrgLine(p_oldorgid,2,1));
				
	    select GROUP_CONCAT(title SEPARATOR '>') into p_orgpath from md_organization where find_in_set(orgid,oFN_FindOrgLine(p_orgid,2,1));
			
			select title into p_oldpostname from md_position where postid in(select Old_Postid from eu_empchange where id=P_ID);
			
      select title into p_postname from md_position where postid in(select postid from eu_empchange where id=P_ID);
			
			select title into p_parentid from md_employee where empid in(select parentid from eu_empchange where id=P_ID);
			
			-- select title into p_postcustom6 from md_codes where id in(select POSTCUSTOM5 from eu_pre_empinfo where APPLYID=P_APPLYID);
			
			/*
		  select REPLACE(MailText,'【员工编号】',p_empno) into p_body
						 from mc_mailtemplate WHERE id=4;
			select REPLACE(p_body,'【员工姓名】',p_title) into p_body;
		  select REPLACE(p_body,'【用工类型】',p_emptype) into p_body;
			select REPLACE(p_body,'【组织路径】',p_orgpath) into p_body;	
			select REPLACE(p_body,'【岗位名称】',p_postname) into p_body;
			select REPLACE(p_body,'【入职日期】',P_Createdate) into p_body;	*/	 
		select mailtitle,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(MailText,'【员工编号】',ifnull(p_empno,'')),'【员工姓名】',ifnull(p_title,'')),'【用工类型】',ifnull(p_emptype,'')),'【原部门】',ifnull(p_oldorgpath,'')),'【原岗位】',ifnull(p_oldpostname,'')),'【新组织】',ifnull(p_orgpath,'')),'【日期】',p_date),'【新岗位】',ifnull(p_postname,'')),'【直属主管】',ifnull(p_parentid,'')),'【换岗日期】',ifnull(p_changedate,'')),MailAttach 
		   into p_subject,p_body,P_Attachment
			 from mc_mailtemplate WHERE id=16;
			 
			 -- 抄送对象		 
			select ofn_empemail(orgid) into P_MailCc from md_organization where orgid=p_oldorgid;
			
			select CONCAT(P_MailCc,';',ofn_empemail(orgid)) into P_MailCc from md_organization where orgid=p_orgid;
			
			insert into tmp_Email(Email)
			select SUBSTRING_INDEX(SUBSTRING_INDEX(P_MailCc,';',b.help_topic_id+1),';',-1) as Email
      from mysql.help_topic b
      where b.help_topic_id<(Length(P_MailCc)-Length(REPLACE(P_MailCc,';',''))+1);
			
			select GROUP_CONCAT(DISTINCT a.email order by c.ORDERBY,a.id asc SEPARATOR ';') into P_MailCc
			from tmp_Email a left join md_employee b on a.email=b.email
											 left join md_codes c on b.postm2=c.id
			where a.email <>(select email from md_employee where empno=p_empno);  #by lynx 不抄送本人
									 
      drop table tmp_Email;		
			
      insert into mc_mailitem(ConfigID,MailTo,MailCc,`SUBJECT`,Body,Attachment)
			select 2,p_mailto,p_mailCc,p_subject,p_body,p_attachment;
			
			-- select p_postname;
			
			END IF;

    END IF;
	
-- 发送离职通知邮件
    IF P_XTYPE = 7 THEN
			-- 插入发送邮件
			set p_date=CONCAT(YEAR(NOW()),'年',MONTH(NOW()),'月',DAY(NOW()),'日');
			select OrgID,CONCAT(date_format(LastWorkdate,'%Y-%m-%d'),oFN_week(LastWorkdate)) into p_orgid,p_lastworkdate 
			from eu_empchange where id=P_ID;
		  select empno,title into p_empno,p_title from eu_empchange where id=P_ID;
		
	    -- 主送对象
			select CONCAT(email1,';',email2) into P_MailTo from ep_email;	
			
			select title into p_EmpType from md_codes where id in(select emptype from md_employee where empid=(select empid from eu_empchange where id=P_ID));			
				
	    select GROUP_CONCAT(title SEPARATOR '>') into p_orgpath from md_organization where find_in_set(orgid,oFN_FindOrgLine(p_orgid,2,1));
			
      select title into p_postname from md_position where postid in(select postid from eu_empchange where id=P_ID);
			
			-- select title into p_postcustom6 from md_codes where id in(select POSTCUSTOM5 from eu_pre_empinfo where APPLYID=P_APPLYID);
			
			/*
		  select REPLACE(MailText,'【员工编号】',p_empno) into p_body
						 from mc_mailtemplate WHERE id=4;
			select REPLACE(p_body,'【员工姓名】',p_title) into p_body;
		  select REPLACE(p_body,'【用工类型】',p_emptype) into p_body;
			select REPLACE(p_body,'【组织路径】',p_orgpath) into p_body;	
			select REPLACE(p_body,'【岗位名称】',p_postname) into p_body;
			select REPLACE(p_body,'【入职日期】',P_Createdate) into p_body;	*/	 
		select  REPLACE(mailtitle,'工号-姓名',concat(ifnull(p_empno,''),'-',ifnull(P_title,''))) ,-- mailtitle,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(MailText,'【员工编号】',ifnull(p_empno,'')),'【员工姓名】',ifnull(p_title,'')),
				'【用工类型】',ifnull(p_emptype,'')),'【原部门】',ifnull(p_orgpath,'')),'【原岗位】',ifnull(p_postname,'')),'【日期】',p_date),'【最后工作日期】',ifnull(p_lastworkdate,'')),MailAttach 
		   into p_subject,p_body,P_Attachment
			 from mc_mailtemplate WHERE id=17;
			 
			 -- 抄送对象		 
			select ofn_empemail(orgid) into P_MailCc from md_organization where orgid=p_orgid;
			
			insert into tmp_Email(Email)
			select SUBSTRING_INDEX(SUBSTRING_INDEX(P_MailCc,';',b.help_topic_id+1),';',-1) as Email
      from mysql.help_topic b
      where b.help_topic_id<(Length(P_MailCc)-Length(REPLACE(P_MailCc,';',''))+1);
			
			select GROUP_CONCAT(DISTINCT a.email order by c.ORDERBY asc SEPARATOR ';') into P_MailCc
			from tmp_Email a left join md_employee b on a.email=b.email
											 left join md_codes c on b.postm2=c.id;

    	drop table tmp_Email;		
			
      insert into mc_mailitem(ConfigID,MailTo,MailCc,`SUBJECT`,Body,Attachment)
			select 2,p_mailto,p_mailCc,p_subject,p_body,p_attachment;
			
			-- select p_postname;

    END IF;		


-- 稽核邮件
    IF P_XTYPE = 17 THEN
			-- 插入发送邮件
			set p_date=CONCAT(YEAR(NOW()),'年',MONTH(NOW()),'月',DAY(NOW()),'日');
			select OrgID,CONCAT(date_format(LastWorkdate,'%Y-%m-%d'),oFN_week(LastWorkdate)) into p_orgid,p_lastworkdate
			from eu_empchange where id=P_ID;
		  select empno,title into p_empno,p_title from eu_empchange where id=P_ID;

	    -- 主送对象
			select CONCAT(email11) into P_MailTo from ep_email;

			select title into p_EmpType from md_codes where id in(select emptype from md_employee where empid=(select empid from eu_empchange where id=P_ID));

	    select GROUP_CONCAT(title SEPARATOR '>') into p_orgpath from md_organization where find_in_set(orgid,oFN_FindOrgLine(p_orgid,2,1));

      select title into p_postname from md_position where postid in(select postid from eu_empchange where id=P_ID);


		select  REPLACE(mailtitle,'工号-姓名',concat(ifnull(p_empno,''),'-',ifnull(P_title,''))) ,-- mailtitle,
				REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(MailText,'【员工编号】',ifnull(p_empno,'')),'【员工姓名】',ifnull(p_title,'')),
				'【用工类型】',ifnull(p_emptype,'')),'【原部门】',ifnull(p_orgpath,'')),'【原岗位】',ifnull(p_postname,'')),'【日期】',p_date),'【最后工作日期】',ifnull(p_lastworkdate,'')),MailAttach
		   into p_subject,p_body,P_Attachment
			 from mc_mailtemplate WHERE id=33;

			 -- 抄送对象
			select ofn_empemail(orgid) into P_MailCc from md_organization where orgid=p_orgid;

			insert into tmp_Email(Email)
			select SUBSTRING_INDEX(SUBSTRING_INDEX(P_MailCc,';',b.help_topic_id+1),';',-1) as Email
      from mysql.help_topic b
      where b.help_topic_id<(Length(P_MailCc)-Length(REPLACE(P_MailCc,';',''))+1);

			select GROUP_CONCAT(DISTINCT a.email order by c.ORDERBY asc SEPARATOR ';') into P_MailCc
			from tmp_Email a left join md_employee b on a.email=b.email
											 left join md_codes c on b.postm2=c.id;

    	drop table tmp_Email;

      insert into mc_mailitem(ConfigID,MailTo,MailCc,`SUBJECT`,Body,Attachment)
			select 2,p_mailto,p_mailCc,p_subject,p_body,p_attachment;


    END IF;
  IF p_error = 2 THEN
    ROLLBACK;
    SELECT
      p_retval;
  END IF;

  COMMIT;
  SET autocommit = 1;
 END;

