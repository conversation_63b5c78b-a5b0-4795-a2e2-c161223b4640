import requests

# 根据corpid和corpsecret获取企业微信的token 
def get_access_token(corpid, corpsecret):
    QY_WECHAT_ACCESS_TOCKEN_URL = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    response = requests.get(QY_WECHAT_ACCESS_TOCKEN_URL)
    return response.json()["access_token"]

# 构建消息体
def build_message_body(touser, msgtype, agentid, text):
    return {
        "touser": touser,
        "msgtype": msgtype,
        "agentid": agentid,
        "text": {
            "content": text
        }
    }

# 发送消息
def send_message(access_token, message_body):
    QY_WECHAT_SEND_MESSAGE_URL = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
    response = requests.post(QY_WECHAT_SEND_MESSAGE_URL, json=message_body)
    return response.json()

def main():
    # 企业微信配置
    corpid = "your_corpid"  # 替换为您的企业ID
    corpsecret = "your_corpsecret"  # 替换为您的应用密钥
    agentid = 1000001  # 替换为您的应用ID
    
    try:
        # 获取access_token
        access_token = get_access_token(corpid, corpsecret)
        
        # 构建消息
        message = build_message_body(
            touser="UserID1|UserID2",  # 接收消息的用户ID，多个用|分隔
            msgtype="text",
            agentid=agentid,
            text="Hello, this is a test message!"
        )
        
        # 发送消息
        result = send_message(access_token, message)
        
        # 打印结果
        if result.get("errcode") == 0:
            print("消息发送成功！")
        else:
            print(f"消息发送失败：{result.get('errmsg')}")
            
    except Exception as e:
        print(f"发生错误：{str(e)}")

if __name__ == "__main__":
    main()
