# FROM harbor-c7n.csleasing.com.cn/kylin/javabase:openjdk-8u432-x64-with-office
# FROM FROM harbor-c7n.csleasing.com.cn/basekylin/javabase:1.8.0.442
FROM harbor-c7n.csleasing.com.cn/basekylin/javabase:1.8.0.442-with-office

COPY bpm-runtime.jar /bpm-runtime.jar
# COPY ./start.sh /start.sh
COPY wqy-microhei.ttc /usr/share/fonts/wqy-microhei/wqy-microhei.ttc
COPY wqy-zenhei.ttc /usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc
EXPOSE 8087
CMD  /opt/openoffice4/program/soffice -headless -accept="socket,host=127.0.0.1,port=8100;urp;" -nofirststartwizard
# RUN chmod 777 /start.sh
# ENTRYPOINT ["/start.sh"]
CMD exec java -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap $JAVA_OPTS -jar /bpm-runtime.jar