select cppr.review_code as                                 '评审编号',
       cppr.review_name as                                 '评审名称',
       cppa.po_apply_code                                  '采购立项编号',
       cppa.po_apply_name                                  '采购立项名称',
       psc.supplier_category_name                          '供应商类别',
       psc.department_id                                   '*************供应商归口部门',
       psccum.unit_name                                    '供应商归口部门',
       cppa.po_apply_description                           '立项说明',
       cppa.apply_project_amount                           '立项申请金额(元)',
       cppa.project_amount                                 '立项通过金额(元)',
       cppa.project_amount - sub_total.total               '立项可用金额(元)',
       ppr.purchase_amount  - sum(ifnull(cfp.payment_amount,0)) '评审剩余可用金额' 
       cppr.purchase_amount                                '评审金额（元）',
       cppr.apply_user_id                                  '***********申请人',
       '申请人',
       cppr.apply_department_id                            '*************申请部门',
       cpprcum.unit_name                                   '申请部门',
       cppr.apply_date                                     '申请时间',
       case cppr.purchase_type_code
           when 'lycg' then '单一来源采购'
           when 'tpcg' then '竞争性谈判采购'
           when 'xjcg' then '询价采购'
           when 'zbcg' then '招标采购'
           else cppr.purchase_type_code end                '采购方式',
       case cppr.purchase_method_code
           when 'emergency' then '不可预见的紧急情况'
           when 'service_consistency' then '原有项目一致性或服务配套要求'
           when 'inquiry' then '询价采购'
           when 'overall_score' then '综合评价法'
           when 'rating_lowest' then '最低评价法'
           when 'public' then '公开招标'
           when 'invite' then '邀请招标'
           when 'irreplaceable_patents' then '不可替代的专利、专有技术或特殊要求项目'
           else cppr.purchase_method_code end              '采购依据/方法',
       cppa.bill_code                                      '总经办编号',
       case cppr.review_status_code
           when 'NEW' then '新建'
           when 'SUBMITTED' then '审批中'
           when 'APPROVED' then '审批通过'
           when 'REJECTED' then '审批拒绝'
           when 'INVALIDED' then '已废止'
           else cppr.review_status_code end                '审批状态',
       case cppr.carry_flag when 1 then '是' else '否' end '是否结转',
       case cppr.review_type_code
           when 'ONLINE' then '线上评审'
           when 'OFFLINE' then '线下评审'
           when 'SKIP' then '免评审'
           else cppr.review_type_code end                  '评审方式',
       cppa.purchase_object                                '采购主体',
       cpprd.supplier_num                                  '供应商编号',
       cs.enterprise_full_name                             '供应商名称',
       case cpprd.selected_flag  when 1 then '是' else '否' end              '是否选用',
       case cpprd.contract_flag  when 1 then '是' else '否' end                              '是否会签署采购合同'
from cloa_po_purchase_apply cppa
         left join cloa_po_purchase_review cppr on cppa.po_apply_id = cppr.po_apply_id
         left join cloa_purchase.cloa_po_purchase_review_detail cpprd on cppr.po_review_id = cpprd.po_review_id
         left join cloa_po_supplier_category psc on cppa.supplier_category_id = psc.supplier_category_id

         left join cloa_purchase.cloa_supplier cs on cpprd.supplier_id = cs.supplier_id

         left join (select icppr.po_apply_id, sum(icppr.purchase_amount) total
                    from cloa_po_purchase_review icppr
                    where icppr.review_status_code in ('APPROVED', 'SUBMITTED')
                    group by icppr.po_apply_id) sub_total on sub_total.po_apply_id = cppa.po_apply_id
         left join cloa_purchase.cloa_unit_mapping psccum on psc.department_id = psccum.unit_code
         left join cloa_purchase.cloa_unit_mapping cpprcum on cppr.apply_department_id = cpprcum.unit_id;




select ppr.review_code ,ppr.purchase_amount,sum(ifnull(cfp.payment_amount,0) ) '已支付金额', ppr.purchase_amount  - sum(ifnull(cfp.payment_amount,0)) '评审剩余可用金额' FROM cloa_po_purchase_review ppr left join cloa_purchase_execute cpe on ppr.po_review_id = cpe.supplier_review_id
left join cloa_fund_payment cfp on cpe.purchase_execute_id = cfp.purchase_execute_id  and  cfp.payment_check_status = 'FINISH'
group by ppr.review_code,ppr.purchase_amount;