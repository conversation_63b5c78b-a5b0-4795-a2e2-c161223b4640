import pandas as pd
import numpy as np
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def compare_excel_columns_with_highlight(file_path, sheet_name=None, output_file=None):
    """
    读取Excel文件，比较G、H、I三列，创建副本并将不同的行标红
    
    参数:
    file_path: Excel文件路径
    sheet_name: 工作表名称（可选，默认为第一个工作表）
    output_file: 输出文件路径（可选，默认为原文件名_highlighted.xlsx）
    """
    try:
        # 读取Excel文件
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        else:
            df = pd.read_excel(file_path)
        
        print(f"成功读取Excel文件，共有 {len(df)} 行数据")
        
        # 检查是否有G、H、I列
        columns = df.columns.tolist()
        print(f"Excel文件的列名: {columns}")
        
        # 获取G、H、I列的索引（假设从A开始为0）
        g_col = 6  # G列
        h_col = 7  # H列
        i_col = 8  # I列
        
        # 如果列数不够，使用列名
        if len(columns) <= max(g_col, h_col, i_col):
            print("使用列名方式获取G、H、I列...")
            # 尝试直接使用列名
            try:
                g_data = df['G'] if 'G' in df.columns else df.iloc[:, g_col] if len(df.columns) > g_col else None
                h_data = df['H'] if 'H' in df.columns else df.iloc[:, h_col] if len(df.columns) > h_col else None
                i_data = df['I'] if 'I' in df.columns else df.iloc[:, i_col] if len(df.columns) > i_col else None
            except:
                print("无法找到G、H、I列，请检查文件格式")
                return
        else:
            # 使用索引获取G、H、I列
            g_data = df.iloc[:, g_col]
            h_data = df.iloc[:, h_col]
            i_data = df.iloc[:, i_col]
        
        # 检查数据是否成功获取
        if g_data is None or h_data is None or i_data is None:
            print("无法获取G、H、I列数据")
            return
        
        print(f"\nG列数据预览: {g_data.head()}")
        print(f"H列数据预览: {h_data.head()}")
        print(f"I列数据预览: {i_data.head()}")
        
        # 比较三列数据，记录不同的行
        different_rows = []
        different_row_indices = []
        
        for index in range(len(df)):
            g_val = g_data.iloc[index]
            h_val = h_data.iloc[index]
            i_val = i_data.iloc[index]
            
            # 处理NaN值
            g_val = g_val if pd.notna(g_val) else None
            h_val = h_val if pd.notna(h_val) else None
            i_val = i_val if pd.notna(i_val) else None
            
            # 检查三个值是否不同
            values = [g_val, h_val, i_val]
            unique_values = set(values)
            
            # 如果有超过1个不同的值，则认为该行有差异
            if len(unique_values) > 1:
                different_rows.append({
                    'row_number': index + 1,  # 行号从1开始
                    'G': g_val,
                    'H': h_val,
                    'I': i_val
                })
                different_row_indices.append(index)
        
        # 打印结果
        print(f"\n=== 比较结果 ===")
        print(f"总行数: {len(df)}")
        print(f"G、H、I列不同的行数: {len(different_rows)}")
        print(f"相同的行数: {len(df) - len(different_rows)}")
        
        if different_rows:
            print(f"\n不同的行详情:")
            print("-" * 50)
            for row in different_rows:
                print(f"行号: {row['row_number']}, G: {row['G']}, H: {row['H']}, I: {row['I']}")
        else:
            print("\n所有行的G、H、I列都相同!")
        
        # 创建输出文件名
        if output_file is None:
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_file = f"{base_name}_highlighted.xlsx"
        
        # 创建高亮的Excel文件
        create_highlighted_excel(df, different_row_indices, output_file)
        
        return different_rows, output_file
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None, None

def create_highlighted_excel(df, different_row_indices, output_file):
    """
    创建带有高亮的Excel文件
    
    参数:
    df: 数据框
    different_row_indices: 需要高亮的行索引列表
    output_file: 输出文件路径
    """
    try:
        # 创建一个新的工作簿
        from openpyxl import Workbook
        wb = Workbook()
        ws = wb.active
        
        # 将数据写入工作表
        for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=True), 1):
            for c_idx, value in enumerate(row, 1):
                ws.cell(row=r_idx, column=c_idx, value=value)
        
        # 定义红色填充样式
        red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
        
        # 高亮不同的行（注意：需要加1因为有表头，再加1因为openpyxl从1开始计数）
        for row_idx in different_row_indices:
            excel_row = row_idx + 2  # 数据行号 = DataFrame索引 + 2（表头+1，openpyxl从1开始）
            
            # 高亮整行
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=excel_row, column=col)
                cell.fill = red_fill
        
        # 保存文件
        wb.save(output_file)
        print(f"\n高亮文件已保存为: {output_file}")
        print(f"共有 {len(different_row_indices)} 行被标记为红色")
        
    except Exception as e:
        print(f"创建高亮文件时出错: {e}")
        
        # 如果openpyxl方法失败，尝试使用pandas的样式功能
        try:
            print("尝试使用pandas样式功能...")
            
            def highlight_different_rows(row):
                # 创建一个与行长度相同的样式列表
                styles = [''] * len(row)
                
                # 如果这行在不同行索引中，则设置红色背景
                if row.name in different_row_indices:
                    styles = ['background-color: red'] * len(row)
                
                return styles
            
            # 应用样式并保存
            styled_df = df.style.apply(highlight_different_rows, axis=1)
            styled_df.to_excel(output_file, engine='openpyxl', index=False)
            print(f"使用pandas样式功能保存文件: {output_file}")
            
        except Exception as e2:
            print(f"pandas样式功能也失败: {e2}")
            # 至少保存一个普通的Excel文件
            df.to_excel(output_file, index=False)
            print(f"保存了普通Excel文件: {output_file}")
            print("请手动标记以下行为红色:")
            for idx in different_row_indices:
                print(f"第 {idx + 2} 行 (包含表头)")  # +2是因为有表头且Excel从1开始计数

# 使用示例
if __name__ == "__main__":
    # 替换为你的Excel文件路径
    file_path = "abc.xlsx"
    
    # 方法1: 使用默认工作表，自动生成输出文件名
    result, output_file = compare_excel_columns_with_highlight(file_path)
    
    # 方法2: 指定工作表名称和输出文件名
    # result, output_file = compare_excel_columns_with_highlight(
    #     file_path, 
    #     sheet_name="Sheet1", 
    #     output_file="custom_highlighted.xlsx"
    # )
    
    # 输出处理结果
    if result is not None:
        print(f"\n处理完成！")
        print(f"高亮文件保存位置: {output_file}")
        if result:
            print(f"共找到 {len(result)} 行数据不一致")
        else:
            print("所有行的G、H、I列数据都一致")
    else:
        print("处理失败，请检查文件路径和格式")