﻿SET FOREIGN_KEY_CHECKS = 0;

DELETE from mc_functions where id in (320517,320310); -- 功能 
DELETE from mc_functioncols where fcid in (320517,320310); -- 功能列
DELETE from mc_functiontools where fcid  in (320517,320310); -- 功能按钮
DELETE from mc_functoolparams where fcid  in (320517,320310); -- 功能按钮参数
DELETE from mc_functionparams  where fcid  in (320517,320310); -- 功能参数
/*SELECT *  from mc_functions where id in (320517,320310); -- 功能*/
 
/*SELECT *  from mc_functioncols where fcid in (320517,320310); -- 功能列*/

/*SELECT *  from mc_functiontools where fcid  in (320517,320310) ;-- 功能按钮*/

/*SELECT *  from mc_functoolparams where fcid  in  (320517,320310)  ;-- 功能按钮参数*/

/*SELECT *  from mc_functionparams  where fcid  in  (320517,320310)  ;-- 功能参数*/


/*
delete from mc_applications where id in ();  -- 应用
delete from mc_applicationparams where appid in ();  -- 应用参数
SELECT *  from mc_applications where id in ();  -- 应用
SELECT *  from mc_applicationparams where appid in ();  -- 应用参数

*/



INSERT INTO mc_functiontools (ID, FCID, TBKey, Title, Title1, Title2, Title3, Title4, Title5, xType, Attribute, ParamLayout, xOrder) VALUES
(1643, 320310, 'TB01', '发起流程', '', '', '', '', '', 'execsp', '<tool>\n  <sqlbeforeclick>WfSp_initiate_beforeCheck(${WF0310.id},${WF0310.flowKey},${WF0310.status},1);</sqlbeforeclick>\n  <sqlwhenclick>api_wf_upload(${WF0310.ID},${WF0310.flowKey},${P_EMPID});wfsp_TableAttachAfter(${WF0310.flowKey},${WF0310.id});api_wf_initiate(${WF0310.id},${WF0310.flowKey},${P_EMPID});WfSp_initiate_after(${WF0310.id},${WF0310.flowKey});</sqlwhenclick>\n  <sqlafterclick></sqlafterclick>\n  <linkcol></linkcol>\n  <hidecol></hidecol>\n  <confirm>0</confirm>\n  <refresh>1</refresh>\n  <cssclass>btn-info</cssclass>\n  <icon>glyphicon glyphicon-pencil</icon>\n  <toolprop></toolprop>\n  <toolstyle></toolstyle>\n</tool>', '', 2);

INSERT INTO mc_functiontools (ID, FCID, TBKey, Title, Title1, Title2, Title3, Title4, Title5, xType, Attribute, ParamLayout, xOrder) VALUES
(1644, 320310, 'TB02', '重新发起', '', '', '', '', '', 'execsp', '<tool>\n  <sqlbeforeclick>WfSp_initiate_beforeCheck(${WF0310.id},${WF0310.flowKey},${WF0310.status},2);</sqlbeforeclick>\n  <sqlwhenclick>api_wf_upload(${WF0310.ID},${WF0310.flowKey},${P_EMPID});wfsp_TableAttachAfter(${WF0310.flowKey},${WF0310.id});api_wf_ReInitiate(${WF0310.instid},${WF0310.flowKey},${WF0310.id},${P_EMPID});WfSp_initiate_after(${WF0310.id},${WF0310.flowKey});</sqlwhenclick>\n  <sqlafterclick></sqlafterclick>\n  <linkcol></linkcol>\n  <hidecol></hidecol>\n  <confirm>0</confirm>\n  <refresh>1</refresh>\n  <cssclass>btn-info</cssclass>\n  <icon>glyphicon glyphicon-pencil</icon>\n  <toolprop></toolprop>\n  <toolstyle></toolstyle>\n</tool>', '', 3);

INSERT INTO mc_functiontools (ID, FCID, TBKey, Title, Title1, Title2, Title3, Title4, Title5, xType, Attribute, ParamLayout, xOrder) VALUES
(1645, 320310, 'TB03', '查看审批信息', '', '', '', '', '', 'popapp', '<tool>\n  <sqlbeforeclick>WfSp_flowopinions_beforeDelete(${WF0310.instid});select ${WF0310.instid} as PARAM2 ;api_WF_review(${PARAM2});select 0;</sqlbeforeclick>\n  <sqlwhenclick></sqlwhenclick>\n  <sqlafterclick></sqlafterclick>\n  <linkcol></linkcol>\n  <hidecol></hidecol>\n  <popapp>320001</popapp>\n  <popappcondi><![CDATA[${WF0310.instid}=${WF0205.procInstId}]]></popappcondi>\n  <confirm>0</confirm>\n  <refresh>1</refresh>\n  <cssclass>btn-info</cssclass>\n  <icon>glyphicon glyphicon-pencil</icon>\n  <size></size>\n  <toolprop></toolprop>\n  <toolstyle></toolstyle>\n</tool>', '', 4);

INSERT INTO mc_functiontools (ID, FCID, TBKey, Title, Title1, Title2, Title3, Title4, Title5, xType, Attribute, ParamLayout, xOrder) VALUES
(1976, 320517, 'TB01', '发起流程', '', '', '', '', '', 'execsp', '<tool>\n  <sqlbeforeclick>WfSp_initiate_beforeCheck(${WF0517.id},${WF0517.flowKey},${WF0517.status},1);</sqlbeforeclick>\n  <sqlwhenclick>api_wf_upload(${WF0517.ID},${WF0517.flowKey},${P_EMPID});wfsp_TableAttachAfter(${WF0517.flowKey},${WF0517.id});api_wf_initiate(${WF0517.id},${WF0517.flowKey},${P_EMPID});WfSp_initiate_after(${WF0517.id},${WF0517.flowKey});</sqlwhenclick>\n  <sqlafterclick></sqlafterclick>\n  <linkcol></linkcol>\n  <hidecol></hidecol>\n  <confirm>0</confirm>\n  <refresh>1</refresh>\n  <cssclass>btn-info</cssclass>\n  <icon>glyphicon glyphicon-pencil</icon>\n  <toolprop></toolprop>\n  <toolstyle></toolstyle>\n</tool>', '', 2);

INSERT INTO mc_functiontools (ID, FCID, TBKey, Title, Title1, Title2, Title3, Title4, Title5, xType, Attribute, ParamLayout, xOrder) VALUES
(1977, 320517, 'TB02', '重新发起', '', '', '', '', '', 'execsp', '<tool>\n  <sqlbeforeclick>WfSp_initiate_beforeCheck(${WF0517.id},${WF0517.flowKey},${WF0517.status},2);</sqlbeforeclick>\n  <sqlwhenclick>api_wf_upload(${WF0517.ID},${WF0517.flowKey},${P_EMPID});wfsp_TableAttachAfter(${WF0517.flowKey},${WF0517.id});api_wf_ReInitiate(${WF0517.instid},${WF0517.flowKey},${WF0517.id},${P_EMPID});WfSp_initiate_after(${WF0517.id},${WF0517.flowKey});</sqlwhenclick>\n  <sqlafterclick></sqlafterclick>\n  <linkcol></linkcol>\n  <hidecol></hidecol>\n  <confirm>0</confirm>\n  <refresh>1</refresh>\n  <cssclass>btn-info</cssclass>\n  <icon>glyphicon glyphicon-pencil</icon>\n  <toolprop></toolprop>\n  <toolstyle></toolstyle>\n</tool>', '', 3);

INSERT INTO mc_functiontools (ID, FCID, TBKey, Title, Title1, Title2, Title3, Title4, Title5, xType, Attribute, ParamLayout, xOrder) VALUES
(1978, 320517, 'TB03', '查看审批信息', '', '', '', '', '', 'popapp', '<tool>\n  <sqlbeforeclick>WfSp_flowopinions_beforeDelete(${WF0517.instid});select ${WF0517.instid} as PARAM2 ;api_WF_review(${PARAM2});select 0;</sqlbeforeclick>\n  <sqlwhenclick></sqlwhenclick>\n  <sqlafterclick></sqlafterclick>\n  <linkcol></linkcol>\n  <hidecol></hidecol>\n  <popapp>320001</popapp>\n  <popappcondi><![CDATA[${WF0517.instid}=${WF0205.procInstId}]]></popappcondi>\n  <confirm>0</confirm>\n  <refresh>1</refresh>\n  <cssclass>btn-info</cssclass>\n  <icon>glyphicon glyphicon-pencil</icon>\n  <size></size>\n  <toolprop></toolprop>\n  <toolstyle></toolstyle>\n</tool>', '', 4);


INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60537, 320310, 'id', NULL, '', '', '', '', 'id', 'a', 'id', '', True, True, 10, 0, 20, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>1</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60538, 320310, '流程实例代码', '', '', '', '', '', 'flowKey', 'a', 'flowKey', '', False, False, 50, 0, 40, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60539, 320310, '离职类型-后台', NULL, '', '', '', '', 'xtype', 'a', 'xtype', '', False, False, 10, 0, 50, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60540, 320310, '申请人', NULL, '', '', '', '', 'RegByEmpno', 'a', 'RegByEmpno', '', False, False, 10, 0, 70, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>SinglePerson</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[all]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60541, 320310, '申请部门', NULL, '', '', '', '', 'RegOrgCode', 'a', 'RegOrgCode', '', False, False, 10, 0, 80, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>SingleTreeBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[organization[all]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60542, 320310, '申请日期', NULL, '', '', '', '', 'RegTime', 'a', 'RegTime', '', False, False, 0, 0, 90, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60543, 320310, '工号', NULL, '', '', '', '', 'Empno', 'a', 'Empno', '', False, False, 10, 0, 110, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[usable]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60544, 320310, '姓名', '', '', '', '', '', 'title', 'a', 'title', '', False, False, 255, 0, 120, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60545, 320310, '用工形式', NULL, '', '', '', '', 'empType', 'a', 'empType', '', False, False, 10, 0, 130, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[emptype]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60547, 320310, '岗位名称', '', '', '', '', '', 'postName', 'a', 'postName', '', False, False, 10, 0, 190, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[position[all]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60548, 320310, '入职日期', '', '', '', '', '', 'CreateDate', 'a', 'CreateDate', '', False, False, 0, 0, 200, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60549, 320310, '司龄', NULL, '', '', '', '', 'WorkAge', 'a', 'WorkAge', '', False, False, 18, 4, 210, '<col>\n  <datatype>Number</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>#0.00</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60550, 320310, '离职原因分类', NULL, '', '', '', '', 'ChangeReason', 'a', 'ChangeReason', '', False, False, 10, 0, 250, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select * from md_codes where xtype=''ChangeReason7'' and ifnull(pid,0)=1326]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60551, 320310, '离职原因-员工', NULL, '', '', '', '', 'ChangeReasonDetail', 'a', 'ChangeReasonDetail', '', False, False, 500, 0, 260, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60552, 320310, '预计离职日期', NULL, '', '', '', '', 'YjDisabledDate', 'a', 'YjDisabledDate', '', False, False, 0, 0, 270, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60553, 320310, '附件材料', NULL, '', '', '', '', 'ChangeAttach', 'a', 'ChangeAttach', '', False, False, 5000, 0, 280, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>Attachment</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>4</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60554, 320310, '挽留结果', NULL, '', '', '', '', 'results', 'a', 'results', '', False, False, 500, 0, 310, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select * from wf_ht_lzwl]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60555, 320310, '确认离职日期', NULL, '', '', '', '', 'DisabledDate', 'a', 'DisabledDate', '', False, False, 0, 0, 320, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.results} == 1);]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60556, 320310, '经济补偿说明', NULL, '', '', '', '', 'Remark', 'a', 'Remark', '', False, False, 500, 0, 400, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.SFZFJJBCJ} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60562, 320310, '工作交接人', NULL, '', '', '', '', 'handoverEmp', 'a', 'handoverEmp', '', False, False, 100, 0, 480, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>MultiplePerson</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[onall]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60563, 320310, '期望停止缴纳社保和公积金月份', NULL, '', '', '', '', 'BeneEndDate', 'a', 'BeneEndDate', '', False, False, 0, 0, 470, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60564, 320310, '离职承诺', NULL, '', '', '', '', 'IsWorkHandover', 'a', 'IsWorkHandover', '', False, False, 10, 0, 530, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70714]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60569, 320310, '归还电脑', NULL, '', '', '', '', 'ReturnComputer', 'a', 'ReturnComputer', '', False, False, 10, 0, 540, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id in (70723,70724)]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60570, 320310, '党组织关系转出', NULL, '', '', '', '', 'OrgRelationship', 'a', 'OrgRelationship', '', False, False, 10, 0, 550, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70802]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60571, 320310, '工会办理', NULL, '', '', '', '', 'TheUnion', 'a', 'TheUnion', '', False, False, 10, 0, 560, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70801]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60572, 320310, '归还工牌', NULL, '', '', '', '', 'EmpCredentials', 'a', 'EmpCredentials', '', False, False, 10, 0, 570, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id in (70723,70724)]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60573, 320310, '财务结算', NULL, '', '', '', '', 'FinSettlement', 'a', 'FinSettlement', '', False, False, 10, 0, 580, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70721]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60574, 320310, '稽核结算', NULL, '', '', '', '', 'AuditSettlement', 'a', 'AuditSettlement', '', False, False, 10, 0, 600, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70722]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60575, 320310, '薪酬结算', NULL, '', '', '', '', 'PaySettlement', 'a', 'PaySettlement', '', False, False, 10, 0, 620, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>CheckBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60579, 320310, '归还劳动手册', NULL, '', '', '', '', 'LaborManual', 'a', 'LaborManual', '', False, False, 10, 0, 630, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id in (70723,70725)]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60580, 320310, '流程状态', NULL, '', '', '', '', 'status', 'a', 'status', '', False, False, 10, 0, 30, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select * from wf_status]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60581, 320310, '流程编码', NULL, '', '', '', '', 'instid', 'a', 'instid', '', False, False, 200, 0, 10, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60582, 320310, '个人邮箱确认', NULL, '', '', '', '', 'Email_P', 'a', 'Email_P', '', False, False, 50, 0, 290, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60583, 320310, '申请编号', NULL, '', '', '', '', 'Flowno', 'a', 'Flowno', '', False, False, 50, 0, 60, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60584, 320310, 'p_status', '', '', '', '', '', '', '', 'p_status', '-', False, False, 0, 0, 640, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60585, 320310, '选择员工', NULL, '', '', '', '', 'P_empid', 'a', 'P_empid', '', False, False, 10, 0, 100, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SinglePerson</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[lock(${WF0310.status}==2||${WF0310.status}==3||${WF0310.status}==4);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[onall]]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60586, 320310, '部门', NULL, '', '', '', '', 'DepCode', 'a', 'DepCode', '', False, False, 10, 0, 140, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SingleTreeBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[lock(${WF0310.status}==2||${WF0310.status}==3||${WF0310.status}==4)]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[organization[usable]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60587, 320310, '政治面貌', NULL, '', '', '', '', 'Party', 'a', 'Party', '', False, False, 10, 0, 220, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[Party]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(63490, 320310, '离职原因分类', NULL, NULL, NULL, NULL, NULL, 'qrlzyyfl', 'a', 'qrlzyyfl', '', False, False, 10, 0, 360, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.results} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[SELECT * FROM md_codes WHERE Xtype=''ChangeReason7'' and  ifnull(pid,0)=${wf0310.leavetype}]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(63491, 320310, '离职原因-HR', NULL, NULL, NULL, NULL, NULL, 'qrlzyysm', 'a', 'qrlzyysm', '', False, False, 2550, 0, 340, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.results} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(63493, 320310, '经济补偿金', NULL, NULL, NULL, NULL, NULL, 'sfzfjjbcj', 'a', 'sfzfjjbcj', '', False, False, 10, 0, 350, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.results} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''isleavecal'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(63494, 320310, '经济补偿金额', NULL, NULL, NULL, NULL, NULL, 'jjbcjje', 'a', 'jjbcjje', '', False, False, 18, 2, 390, '<col>\n  <datatype>Number</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.SFZFJJBCJ} == 0);]]></elscript>\n  <format>#0.00</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68216, 320310, '发起离任审计', NULL, NULL, NULL, NULL, NULL, 'isaudit', 'a', 'isaudit', '', False, False, 10, 0, 462, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''leaveSJ'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68217, 320310, '职务层级', NULL, NULL, NULL, NULL, NULL, 'postduty', 'a', 'postduty', '', False, False, 10, 0, 150, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[postduty1]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68218, 320310, '离职类型', NULL, NULL, NULL, NULL, NULL, 'leavetype', 'a', 'leavetype', '', False, False, 10, 0, 240, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.results} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[LeaveType]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68221, 320310, '发送离职证明', NULL, NULL, NULL, NULL, NULL, 'certificate', 'a', 'certificate', '', False, False, 100, 0, 650, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''leaveconfirm'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68224, 320310, 'WFInstanceID', NULL, NULL, NULL, NULL, NULL, 'WFInstanceID', 'a', 'WFInstanceID', '', False, False, 10, 0, 660, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68893, 320310, '职级', NULL, NULL, NULL, NULL, NULL, 'postm2', 'a', 'postm2', '', False, False, 10, 0, 160, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[postm2]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68894, 320310, '合同结束日期', NULL, NULL, NULL, NULL, NULL, 'conenddate', 'a', 'conenddate', '', False, False, 0, 0, 230, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68895, 320310, '说明', NULL, NULL, NULL, NULL, NULL, 'mailnoticeText', 'a', 'mailnoticeText', '', False, False, 100, 0, 300, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68896, 320310, '离职去向分类', NULL, NULL, NULL, NULL, NULL, 'leavePlanType', 'a', 'leavePlanType', '', False, False, 10, 0, 370, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.results} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''leaveplantype'';]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68897, 320310, '离职去向说明', NULL, NULL, NULL, NULL, NULL, 'leavePlanNoticeText', 'a', 'leavePlanNoticeText', '', False, False, 1000, 0, 380, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.results} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68898, 320310, '离任审计-部门', NULL, NULL, NULL, NULL, NULL, 'depOpinion_sj', 'a', 'depOpinion_sj', '', False, False, 10, 0, 410, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''isleaveSJ'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68899, 320310, '离任审计-人力', NULL, NULL, NULL, NULL, NULL, 'HROpinion_sj', 'a', 'HROpinion_sj', '', False, False, 10, 0, 520, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''isleaveSJ'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68900, 320310, '继任者确认', NULL, NULL, NULL, NULL, NULL, 'ishandoverCheck', 'a', 'ishandoverCheck', '', False, False, 10, 0, 670, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''handover'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68901, 320310, '部门确认交接', NULL, NULL, NULL, NULL, NULL, 'isdepcheck', 'a', 'isdepcheck', '', False, False, 10, 0, 690, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''handover1'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(68902, 320310, '培训协议结算', NULL, NULL, NULL, NULL, NULL, 'trainSettlement', 'a', 'trainSettlement', '', False, False, 10, 0, 700, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70803]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69327, 320310, 'isHRBP', NULL, NULL, NULL, NULL, NULL, 'isHRBP', 'a', 'isHRBP', '', False, False, 10, 0, 710, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69373, 320310, '组织名称', NULL, NULL, NULL, NULL, NULL, 'orgpath', 'a', 'orgpath', '', False, False, 1000, 0, 180, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69385, 320310, '组织id', NULL, NULL, NULL, NULL, NULL, 'orgName', 'a', 'orgName', '', False, False, 10, 0, 170, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleTreeBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.P_EMPID} != 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[organization[all]]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69734, 320517, 'id', NULL, '', '', '', '', 'id', 'a', 'id', '', True, True, 10, 0, 571, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>1</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69735, 320517, '流程实例代码', NULL, '', '', '', '', 'flowKey', 'a', 'flowKey', '', False, False, 50, 0, 561, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69736, 320517, '离职类型-后台', NULL, '', '', '', '', 'xtype', 'a', 'xtype', '', False, False, 10, 0, 581, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69737, 320517, '代办人', NULL, '', '', '', '', 'RegByEmpno', 'a', 'RegByEmpno', '', False, False, 10, 0, 33, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SinglePerson</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[all]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69738, 320517, '代办人部门', NULL, '', '', '', '', 'RegOrgCode', 'a', 'RegOrgCode', '', False, False, 10, 0, 21, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleTreeBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[organization[all]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69739, 320517, '申请日期', NULL, '', '', '', '', 'RegTime', 'a', 'RegTime', '', False, False, 0, 0, 601, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69740, 320517, '工号', '', '', '', '', '', 'Empno', 'a', 'Empno', '', False, False, 10, 0, 61, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[usable]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69741, 320517, '姓名', NULL, '', '', '', '', 'title', 'a', 'title', '', False, False, 255, 0, 71, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69742, 320517, '用工形式', '', '', '', '', '', 'empType', 'a', 'empType', '', False, False, 10, 0, 101, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[emptype]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69743, 320517, '岗位名称', NULL, '', '', '', '', 'postName', 'a', 'postName', '', False, False, 10, 0, 111, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[position[all]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69744, 320517, '入职日期', NULL, '', '', '', '', 'CreateDate', 'a', 'CreateDate', '', False, False, 0, 0, 141, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69745, 320517, '司龄', '', '', '', '', '', 'WorkAge', 'a', 'WorkAge', '', False, False, 18, 4, 151, '<col>\n  <datatype>Number</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>#0.00</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69746, 320517, '离职原因分类', NULL, '', '', '', '', 'ChangeReason', 'a', 'ChangeReason', '', False, False, 10, 0, 661, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>1</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select * from md_codes where xtype=''ChangeReason7'' and ifnull(pid,0)=1326]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69747, 320517, '离职原因说明', NULL, '', '', '', '', 'ChangeReasonDetail', 'a', 'ChangeReasonDetail', '', False, False, 65535, 0, 651, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>1</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69748, 320517, '预计离职日期', NULL, '', '', '', '', 'YjDisabledDate', 'a', 'YjDisabledDate', '', False, False, 0, 0, 641, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>1</hidden>\n  <notnull>1</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69749, 320517, '附件材料', NULL, '', '', '', '', 'ChangeAttach', 'a', 'ChangeAttach', '', False, False, 65535, 0, 271, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>Attachment</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>4</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69750, 320517, '挽留结果', NULL, '', '', '', '', 'results', 'a', 'results', '', False, False, 10, 0, 631, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select * from wf_ht_lzwl]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69751, 320517, '确认离职日期', NULL, '', '', '', '', 'DisabledDate', 'a', 'DisabledDate', '', False, False, 0, 0, 181, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69752, 320517, '经济补偿说明', NULL, '', '', '', '', 'Remark', 'a', 'Remark', '', False, False, 65535, 0, 291, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69753, 320517, '工作交接人', NULL, '', '', '', '', 'handoverEmp', 'a', 'handoverEmp', '', False, False, 100, 0, 363, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>MultiplePerson</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[onall]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69754, 320517, '期望停止缴纳社保和公积金月份', NULL, '', '', '', '', 'BeneEndDate', 'a', 'BeneEndDate', '', False, False, 0, 0, 381, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69755, 320517, '离职承诺', NULL, '', '', '', '', 'IsWorkHandover', 'a', 'IsWorkHandover', '', False, False, 10, 0, 411, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70714]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69756, 320517, '归还电脑', NULL, '', '', '', '', 'ReturnComputer', 'a', 'ReturnComputer', '', False, False, 10, 0, 501, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id in (70723,70724)]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69757, 320517, '党组织关系转出', NULL, '', '', '', '', 'OrgRelationship', 'a', 'OrgRelationship', '', False, False, 10, 0, 461, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70802]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69758, 320517, '工会办理', NULL, '', '', '', '', 'TheUnion', 'a', 'TheUnion', '', False, False, 10, 0, 471, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70801]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69759, 320517, '归还工牌', NULL, '', '', '', '', 'EmpCredentials', 'a', 'EmpCredentials', '', False, False, 10, 0, 521, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id in (70723,70724)]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69760, 320517, '财务结算', NULL, '', '', '', '', 'FinSettlement', 'a', 'FinSettlement', '', False, False, 10, 0, 431, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70721]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69761, 320517, '稽核结算', NULL, '', '', '', '', 'AuditSettlement', 'a', 'AuditSettlement', '', False, False, 10, 0, 481, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70722]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69762, 320517, '薪酬结算', NULL, '', '', '', '', 'PaySettlement', 'a', 'PaySettlement', '', False, False, 10, 0, 551, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>CheckBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69763, 320517, '归还劳动手册', NULL, '', '', '', '', 'LaborManual', 'a', 'LaborManual', '', False, False, 10, 0, 531, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id in (70723,70725)]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69764, 320517, '流程状态', NULL, '', '', '', '', 'status', 'a', 'status', '', False, False, 10, 0, 621, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select * from wf_status]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69765, 320517, '流程编码', NULL, '', '', '', '', 'instid', 'a', 'instid', '', False, False, 200, 0, 611, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69766, 320517, '个人邮箱确认', NULL, '', '', '', '', 'Email_P', 'a', 'Email_P', '', False, False, 50, 0, 251, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69767, 320517, '申请编号', NULL, '', '', '', '', 'Flowno', 'a', 'Flowno', '', False, False, 50, 0, 591, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69769, 320517, '选择离职员工', NULL, '', '', '', '', 'P_empid', 'a', 'P_empid', '', False, False, 10, 0, 51, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SinglePerson</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[lock(${WF0517.status}==2||${WF0517.status}==3||${WF0517.status}==4)]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[onall]]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69770, 320517, '部门', NULL, '', '', '', '', 'DepCode', 'a', 'DepCode', '', False, False, 10, 0, 91, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SingleTreeBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[lock(${WF0517.status}==2||${WF0517.status}==3||${WF0517.status}==4)]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[organization[usable]]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69771, 320517, '政治面貌', NULL, '', '', '', '', 'Party', 'a', 'Party', '', False, False, 10, 0, 171, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SinglePopUpList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[Party]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69772, 320517, '离职原因分类', NULL, '', '', '', '', 'qrlzyyfl', 'a', 'qrlzyyfl', '', False, False, 10, 0, 201, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[SELECT * FROM md_codes WHERE Xtype=''ChangeReason7'' and  ifnull(pid,0)=${wf0517.leavetype}]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69773, 320517, '离职原因-HR', NULL, '', '', '', '', 'qrlzyysm', 'a', 'qrlzyysm', '', False, False, 2500, 0, 221, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69774, 320517, '经济补偿金', NULL, '', '', '', '', 'sfzfjjbcj', 'a', 'sfzfjjbcj', '', False, False, 10, 0, 211, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''isleavecal'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69775, 320517, '经济补偿金额', NULL, '', '', '', '', 'jjbcjje', 'a', 'jjbcjje', '', False, False, 18, 2, 281, '<col>\n  <datatype>Number</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>#0.00</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69776, 320517, '发起离任审计', NULL, '', '', '', '', 'isaudit', 'a', 'isaudit', '', False, False, 10, 0, 351, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''leaveSJ'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69777, 320517, '职务层级', NULL, '', '', '', '', 'postduty', 'a', 'postduty', '', False, False, 10, 0, 121, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[postduty1]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69778, 320517, '离职类型', NULL, '', '', '', '', 'leavetype', 'a', 'leavetype', '', False, False, 10, 0, 191, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select * from md_codes where xtype=''leavetype'' and id<=1328]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69779, 320517, '发送离职证明', NULL, '', '', '', '', 'certificate', 'a', 'certificate', '', False, False, 100, 0, 541, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''leaveconfirm'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69780, 320517, 'WFInstanceID', NULL, '', '', '', '', 'WFInstanceID', 'a', 'WFInstanceID', '', False, False, 10, 0, 671, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69781, 320517, '职级', NULL, '', '', '', '', 'postm2', 'a', 'postm2', '', False, False, 10, 0, 131, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[postm2]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69782, 320517, '合同结束日期', NULL, '', '', '', '', 'conenddate', 'a', 'conenddate', '', False, False, 0, 0, 161, '<col>\n  <datatype>DateTime</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>DateTime</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format>yyyy-MM-dd</format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69783, 320517, '说明', NULL, '', '', '', '', 'mailnoticeText', 'a', 'mailnoticeText', '', False, False, 100, 0, 261, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69784, 320517, '离职去向分类', NULL, '', '', '', '', 'leavePlanType', 'a', 'leavePlanType', '', False, False, 10, 0, 231, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''leaveplantype'';]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69785, 320517, '离职去向说明', NULL, '', '', '', '', 'leavePlanNoticeText', 'a', 'leavePlanNoticeText', '', False, False, 1000, 0, 241, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69786, 320517, '离任审计-部门', NULL, '', '', '', '', 'depOpinion_sj', 'a', 'depOpinion_sj', '', False, False, 10, 0, 301, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''isleaveSJ'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69787, 320517, '离任审计-人力', NULL, '', '', '', '', 'HROpinion_sj', 'a', 'HROpinion_sj', '', False, False, 10, 0, 401, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''isleaveSJ'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69788, 320517, '继任者确认', '', '', '', '', '', 'ishandoverCheck', 'a', 'ishandoverCheck', '', False, False, 10, 0, 691, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''handover'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69789, 320517, '部门确认工作交接', NULL, '', '', '', '', 'isdepcheck', 'a', 'isdepcheck', '', False, False, 10, 0, 423, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''handover1'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69790, 320517, '培训协议结算', NULL, '', '', '', '', 'trainSettlement', 'a', 'trainSettlement', '', False, False, 10, 0, 451, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70803]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69791, 320517, 'isHRBP', '', '', '', '', '', 'isHRBP', 'a', 'isHRBP', '', False, False, 10, 0, 761, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69792, 320517, '组织名称', NULL, '', '', '', '', 'orgpath', 'a', 'orgpath', '', False, False, 1000, 0, 81, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69793, 320517, '组织名称', NULL, '', '', '', '', 'orgName', 'a', 'orgName', '', False, False, 10, 0, 80, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.REGORGCODE} != 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69794, 320517, '申请类型', NULL, NULL, NULL, NULL, NULL, 'DBtype', 'a', 'DBtype', '', False, False, 10, 0, 31, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[1]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''dbtype'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(69795, 320517, '代办申明', NULL, NULL, NULL, NULL, NULL, 'DBConfirm', 'a', 'DBConfirm', '', False, False, 10, 0, 41, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''DBConfirm'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(71066, 320310, '【重要提醒】', NULL, NULL, NULL, NULL, NULL, 'tag1', 'a', 'tag1', '', False, False, 65535, 0, 720, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(71084, 320517, '直属上级确认工作交接', NULL, NULL, NULL, NULL, NULL, 'IsParentCheck', 'a', 'IsParentCheck', '', False, False, 10, 0, 421, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''handover'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(71085, 320310, '直属上级确认交接', NULL, NULL, NULL, NULL, NULL, 'IsParentCheck', 'a', 'IsParentCheck', '', False, False, 10, 0, 680, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where xtype=''handover'']]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72608, 320310, '项目风险意见', NULL, NULL, NULL, NULL, NULL, 'ProjectRisk', 'a', 'ProjectRisk', '', False, False, 10, 0, 420, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0||${WF0310.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[ProjectRisk]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72609, 320310, '风险说明(如有)', NULL, NULL, NULL, NULL, NULL, 'ProjectRiskOpin', 'a', 'ProjectRiskOpin', '', False, False, 1000, 0, 430, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0||${WF0310.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72610, 320310, '稽核初审意见1', NULL, NULL, NULL, NULL, NULL, 'CheckingOpinion', 'a', 'CheckingOpinion', '', False, False, 10, 0, 440, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[CheckingOpinion]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72611, 320310, '稽核初审说明', NULL, NULL, NULL, NULL, NULL, 'CheckingExplain', 'a', 'CheckingExplain', '', False, False, 1000, 0, 450, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72612, 320310, '协议签署-稽核', NULL, NULL, NULL, NULL, NULL, 'AuditSettlement_Cns', 'a', 'AuditSettlement_Cns', '', False, False, 10, 0, 610, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[AuditSettlement_Cns]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72613, 320310, '签署协议-人力', NULL, NULL, NULL, NULL, NULL, 'IsAuditSettlement_Cns', 'a', 'IsAuditSettlement_Cns', '', False, False, 10, 0, 572, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.AUDITSETTLEMENT_CNS} != 71034);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[IsAuditSettlement_Cns]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72614, 320517, '项目风险提示意见', NULL, NULL, NULL, NULL, NULL, 'ProjectRisk', 'a', 'ProjectRisk', '', False, False, 10, 0, 311, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.isPostType} == 0||${WF0517.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[ProjectRisk]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72615, 320517, '项目风险提示意见说明', NULL, NULL, NULL, NULL, NULL, 'ProjectRiskOpin', 'a', 'ProjectRiskOpin', '', False, False, 1000, 0, 321, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.isPostType} == 0||${WF0517.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72616, 320517, '稽核初审意见', NULL, NULL, NULL, NULL, NULL, 'CheckingOpinion', 'a', 'CheckingOpinion', '', False, False, 10, 0, 331, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[CheckingOpinion]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72617, 320517, '稽核初审说明', NULL, NULL, NULL, NULL, NULL, 'CheckingExplain', 'a', 'CheckingExplain', '', False, False, 1000, 0, 341, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.V1} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72618, 320517, '签署协议-稽核', NULL, NULL, NULL, NULL, NULL, 'AuditSettlement_Cns', 'a', 'AuditSettlement_Cns', '', False, False, 10, 0, 491, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[AuditSettlement_Cns]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72619, 320517, '签署协议-人力', NULL, NULL, NULL, NULL, NULL, 'IsAuditSettlement_Cns', 'a', 'IsAuditSettlement_Cns', '', False, False, 10, 0, 511, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[IsAuditSettlement_Cns]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72620, 320310, '重要提醒:', NULL, NULL, NULL, NULL, NULL, 'DisabledDateR', 'a', 'DisabledDateR', '', False, False, 255, 0, 330, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72621, 320310, 'isPostType', NULL, NULL, NULL, NULL, NULL, NULL, '', 'isPostType', '-', False, False, 0, 0, 730, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72622, 320517, '是否前台人员', NULL, NULL, NULL, NULL, NULL, NULL, '', 'isPostType', '-', False, False, 0, 0, 771, '<col>\n  <datatype>String</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>CheckBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72804, 320310, '资产审核人', NULL, NULL, NULL, NULL, NULL, 'AssetReviewers', 'a', 'AssetReviewers', '', False, False, 10, 0, 490, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[SELECT a.empid AS id ,CONCAT(''资产审核人'',''-'',b.Empno,''-'',b.Title,''('',a.title,''); '',''资产复核人'',''-'',c.Empno,''-'',c.title) AS title ,a.Disabled FROM eu_assetreviewers a ,md_employee b,md_employee c WHERE a.empid=b.EmpID and a.fgempid = c.EmpID and ifnull(a.disabled,0)=0 ORDER BY a.xorder]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72805, 320310, '资产初审意见', NULL, NULL, NULL, NULL, NULL, 'AssetNotes', 'a', 'AssetNotes', '', False, False, 65535, 0, 500, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72806, 320310, '资产初审附件', NULL, NULL, NULL, NULL, NULL, 'AssetAttach', 'a', 'AssetAttach', '', False, False, 65535, 0, 502, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>Attachment</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>4</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72807, 320310, '稽核风险排查', NULL, NULL, NULL, NULL, NULL, 'AuditReview', 'a', 'AuditReview', '', False, False, 10, 0, 506, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[AuditReview]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72808, 320310, '稽核离任审计', NULL, NULL, NULL, NULL, NULL, 'AuditDeparture', 'a', 'AuditDeparture', '', False, False, 10, 0, 460, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.HROPINION_SJ} != 1 || ${WF0310.V1} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[AuditDeparture]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72810, 320517, '资产审核人', NULL, NULL, NULL, NULL, NULL, 'AssetReviewers', 'a', 'AssetReviewers', '', False, False, 10, 0, 371, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>SingleSelect</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.isPostType} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[SELECT a.empid AS id ,CONCAT(''资产审核人'',''-'',b.Empno,''-'',b.Title,''('',a.title,''); '',''资产复核人'',''-'',c.Empno,''-'',c.title) AS title ,a.Disabled FROM eu_assetreviewers a ,md_employee b,md_employee c WHERE a.empid=b.EmpID and a.fgempid = c.EmpID and ifnull(a.disabled,0)=0 ORDER BY a.xorder]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72811, 320517, '资产初审意见', NULL, NULL, NULL, NULL, NULL, 'AssetNotes', 'a', 'AssetNotes', '', False, False, 65535, 0, 373, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.isPostType} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72812, 320517, '资产初审附件', NULL, NULL, NULL, NULL, NULL, 'AssetAttach', 'a', 'AssetAttach', '', False, False, 65535, 0, 374, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>Attachment</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.isPostType} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>4</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72813, 320517, '稽核风险排查', NULL, NULL, NULL, NULL, NULL, 'AuditReview', 'a', 'AuditReview', '', False, False, 10, 0, 377, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.isPostType} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[AuditReview]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72814, 320517, '离任审计-稽核', NULL, NULL, NULL, NULL, NULL, 'AuditDeparture', 'a', 'AuditDeparture', '', False, False, 10, 0, 361, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.HROPINION_SJ} != 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[AuditDeparture]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72816, 320517, '是否实习', NULL, NULL, NULL, NULL, NULL, NULL, '', 'IsSx', '-', False, False, 0, 0, 781, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.REGORGCODE} != 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72817, 320310, '招待用品审核', NULL, NULL, NULL, NULL, NULL, 'Hospitality', 'a', 'Hospitality', '', False, False, 10, 0, 590, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.V1} == 0)]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[Hospitality]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72818, 320517, '招待用品审核', NULL, NULL, NULL, NULL, NULL, 'Hospitality', 'a', 'Hospitality', '', False, False, 10, 0, 441, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[Hospitality]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72821, 320310, '202407版本控制', NULL, NULL, NULL, NULL, NULL, 'V1', 'a', 'V1', '', False, False, 10, 0, 740, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(72822, 320517, '202407版本控制', NULL, NULL, NULL, NULL, NULL, 'V1', 'a', 'V1', '', False, False, 10, 0, 791, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.REGORGCODE} != 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73332, 320310, '交接模板', NULL, NULL, NULL, NULL, NULL, 'Attach2', 'a', 'Attach2', '', False, False, 65535, 0, 760, '<col>\n  <datatype>Blob</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>Attachment</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>4</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73333, 320310, '202412版本控制', NULL, NULL, NULL, NULL, NULL, 'V2', 'a', 'V2', '', False, False, 10, 0, 770, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73341, 320517, '交接模板', '', NULL, NULL, NULL, NULL, 'Attach2', 'a', 'Attach2', NULL, False, False, 500, 0, 801, '<col>\n  <datatype>Blob</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>Attachment</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>4</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73342, 320517, '202412版本控制', NULL, NULL, NULL, NULL, NULL, 'V2', 'a', 'V2', '', False, False, 10, 0, 811, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73364, 320310, '稽核初审意见2', NULL, NULL, NULL, NULL, NULL, 'CheckingOpinion2', 'a', 'CheckingOpinion2', '', False, False, 10, 0, 440, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[CheckingOpinion2]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73365, 320517, '稽核初审意见', NULL, NULL, NULL, NULL, NULL, 'CheckingOpinion2', 'a', 'CheckingOpinion2', '', False, False, 10, 0, 331, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[CheckingOpinion2]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73415, 320310, '20250618版本', NULL, NULL, NULL, NULL, NULL, 'V3', 'a', 'V3', '', False, False, 10, 0, 780, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73417, 320310, 'd', NULL, NULL, NULL, NULL, NULL, 'IsWorkHandoverV3', 'a', 'IsWorkHandoverV3', '', False, False, 10, 0, 800, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0 || ${WF0310.isPostType} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[0]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=71183]]></source>\n  <customcode></customcode>\n  <locked>1</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73418, 320517, '资产复核意见', NULL, NULL, NULL, NULL, NULL, 'AuditReviewText', 'a', 'AuditReviewText', '', False, False, 500, 0, 375, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0517.isPostType} == 0 || ${WF0517.V1} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73419, 320310, '资产复核意见', NULL, NULL, NULL, NULL, NULL, 'AuditReviewText', 'a', 'AuditReviewText', '', False, False, 500, 0, 504, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73420, 320310, '资产逾期情况', NULL, NULL, NULL, NULL, NULL, 'AssetOverdue', 'a', 'AssetOverdue', '', False, False, 10, 0, 810, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>RadioBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.V3} == 1);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[AssetOverdue]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73422, 320310, '稽核风险排查结果', NULL, NULL, NULL, NULL, NULL, 'CheckingExplainV3', 'a', 'CheckingExplainV3', '', False, False, 500, 0, 510, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[hide(${WF0310.V3} == 0);]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73423, 320517, '稽核风险排查结果', NULL, NULL, NULL, NULL, NULL, 'CheckingExplainV3', 'a', 'CheckingExplainV3', '', False, False, 500, 0, 391, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');


INSERT INTO mc_functions (ID, Code, Title, Title1, Title2, Title3, Title4, Title5, xType, GridLayout, FormLayout, ParamLayout, DataSource, SQLXML, AddNewScript, UpdateScript, DataPermission, DataCheck, ToolPermission, OprRemark, Remark, FormValidate, DBAlias) VALUES
(320310, 'WF0310', '离职审批', '', '', '', '', '', 'grid', '<grid column="1" refresh="1" toggle="0" pageswitch="0" checkbox="radiobox" pager="1" pagesize="15" pagelist="15,50,100" group="0" help="0" lockcolkey="" param="0" nowrap="0" height="100%" fixednumber="0" actionindex="1" toolstyle="1" edittype="0" importtplid="0" timestamp="0">\n  <cols colKey="ACTION" align="left" width="" sortable="0" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="INSTID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="STATUS" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="FLOWKEY" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="XTYPE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="FLOWNO" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REGBYEMPNO" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REGORGCODE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REGTIME" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="P_EMPID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="EMPNO" align="left" width="80" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>1</uptlog>\n  </cols>\n  <cols colKey="TITLE" align="left" width="100" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>1</uptlog>\n  </cols>\n  <cols colKey="EMPTYPE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DEPCODE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="POSTDUTY" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="POSTM2" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ORGPATH" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="POSTNAME" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CREATEDATE" align="left" width="100" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="WORKAGE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PARTY" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CONENDDATE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="LEAVETYPE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHANGEREASON" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHANGEREASONDETAIL" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="YJDISABLEDDATE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHANGEATTACH" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="EMAIL_P" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="MAILNOTICETEXT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="RESULTS" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DISABLEDDATE" align="left" width="100" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>2</uptlog>\n  </cols>\n  <cols colKey="DISABLEDDATER" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="QRLZYYSM" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="SFZFJJBCJ" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="QRLZYYFL" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="LEAVEPLANNOTICETEXT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="LEAVEPLANTYPE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="JJBCJJE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REMARK" align="left" width="150" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DEPOPINION_SJ" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PROJECTRISK" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PROJECTRISKOPIN" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHECKINGOPINION" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHECKINGEXPLAIN" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="HROPINION_SJ" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISAUDIT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="BENEENDDATE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="HANDOVEREMP" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISWORKHANDOVER" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="RETURNCOMPUTER" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ORGRELATIONSHIP" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="THEUNION" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="EMPCREDENTIALS" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISAUDITSETTLEMENT_CNS" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="FINSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="AUDITSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="AUDITSETTLEMENT_CNS" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PAYSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="LABORMANUAL" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="P_STATUS" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CERTIFICATE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="WFINSTANCEID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISHANDOVERCHECK" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISPARENTCHECK" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISDEPCHECK" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="TRAINSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISHRBP" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ORGNAME" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="TAG1" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISPOSTTYPE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n</grid>', '<form>\n  <blocks id="1pcy3xl0ba1" type="block" layout="1" direction="0" border="0" showtype="1" rownum="54" colnum="2">\n    <cells code="P_EMPID" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="EMPNO" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="TITLE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="DEPCODE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="ORGPATH" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="EMPTYPE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="POSTNAME" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="POSTDUTY" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="POSTM2" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="CREATEDATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="WORKAGE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="PARTY" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="CONENDDATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="CHANGEREASON" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="YJDISABLEDDATE" tips="eyIwIjoi6K+35LiO55u05bGe5LiK57qn5ZWG5a6a5ZCO5aGr5YaZIn0=" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="CHANGEREASONDETAIL" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="EMAIL_P" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="MAILNOTICETEXT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CHANGEATTACH" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="DISABLEDDATER" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="DISABLEDDATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="LEAVETYPE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="QRLZYYFL" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="SFZFJJBCJ" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="QRLZYYSM" tips="eyIwIjoi56a76IGM5Y6f5Zug5bey5a+556a76IGM5ZGY5bel6ZqQ6JeP77yM5Y+v5oyJ55yf5a6e5oOF5Ya15aGr5YaZIn0=" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="LEAVEPLANTYPE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="LEAVEPLANNOTICETEXT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="JJBCJJE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="REMARK" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="DEPOPINION_SJ" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="PROJECTRISK" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="PROJECTRISKOPIN" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="CHECKINGOPINION" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CHECKINGOPINION2" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CHECKINGEXPLAIN" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="ISAUDIT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="HANDOVEREMP" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ASSETREVIEWERS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ASSETNOTES" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="ASSETATTACH" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITREVIEWTEXT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="AUDITREVIEW" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CHECKINGEXPLAINV3" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="HROPINION_SJ" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITDEPARTURE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISWORKHANDOVER" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISPARENTCHECK" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISDEPCHECK" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="FINSETTLEMENT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="HOSPITALITY" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="TRAINSETTLEMENT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ORGRELATIONSHIP" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="THEUNION" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITSETTLEMENT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITSETTLEMENT_CNS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="RETURNCOMPUTER" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="EMPCREDENTIALS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISAUDITSETTLEMENT_CNS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="LABORMANUAL" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CERTIFICATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="TAG1" tips="" hidetitle="0" titlenum="1" titleclass="font-bold" contclass="font-bold" cellclass="" width="" height="150" rows="1" cols="2"/>\n    <cells code="ATTACH2" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="V1" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n  </blocks>\n</form>', '', 'select case when `status`=1 then 0 else 1 end p_status\n,(SELECT CASE WHEN ifnull(b.PostType,0)=70119 THEN 1 ELSE 0 END FROM md_position b  WHERE a.postName=b.PostID ) AS isPostType\n,a.* \nfrom wf_ht_empchange a where xtype=1 \norder by a.regtime desc', '', 'select ${P_EMPID}  as RegByEmpno,\n${P_orgid} as RegOrgCode,  now()  as RegTime,''lzlc''  as flowKey,1 as status,\n1 as xtype;\n\nselect 71175 as  CheckingOpinion , 71033 as AuditSettlement_Cns;\n\nselect ${P_EMPID} as p_empid;\n\nselect  if ( ${p_empid} in (select approver from wf_approverconfig where id in (12,13)),1,0) as ishrbp ;\n\nselect orgid as RegOrgCode from md_employee where empid=${P_EMPID};\n\nselect a.empno,a.title,a.emptype,a.orgid as orgname,ofn_OrgDepid(a.orgid) as depcode,a.postid as postName,a.createdate,a.comyear as workage,a.party,a.postcustom4 as postduty,a.postm2,a.conenddate,a.email_p,b.orgtpath as orgpath\nfrom md_employee  a ,ov_findorgline_exceptcomp b where a.empid=${P_EMPID} and a.orgid=b.orgid;\n\nselect ''邮箱用于接受《离职证明》，请务必确认准确。'' as  mailnoticeText;\nselect ''请重点关注离职日期，提交后不可修改；若稽核启动风险排查，原则上，离职日期需要延迟到提出之日的30天。'' as DisabledDateR;\n\nselect Remark  as tag1 from md_codes mc  where id=1326 ;\n\nSELECT CASE WHEN ifnull(b.PostType,0)=70119 THEN 1 ELSE 0 END isPostType\n FROM md_position b ,md_employee   a WHERE a.postid=b.PostID  and a.empid=${P_EMPID};\n\nselect 1  as V1;\nselect 1  as V2;\nselect 1  as V3;\nSelect Tempfilepath as Attach2 From mc_importtemplate Where id=2;', 'select empno,title,emptype,orgid as orgname,ofn_OrgDepid(orgid) as depcode,postid as postname,createdate,comyear as workage,party, postcustom4 as postduty,postm2,conenddate,email_p from md_employee where empid=${WF0310.P_empid};\n\nselect orgtpath as orgpath from ov_findorgline_exceptcomp where orgid=(select orgid from md_employee where empid=${WF0310.P_empid});\n\n\nselect 1 as ispromentor where exists(select * from md_employee a ,wf_ht_probreg b where a.empid=b.wfempid and b.promentor=${WF0310.P_empid} and ifnull(a.porbstatus,0)=70162 );\n\nselect cast(truncate(${wf0310.jjbcjje},2) as decimal(17,2)) as jjbcjje;\n\nselect ${WF0310.YjDisabledDate} as disableddate;\n\nSELECT CASE WHEN ifnull(b.PostType,0)=70119 THEN 1 ELSE 0 END isPostType\n FROM md_position b ,md_employee   a WHERE a.postid=b.PostID  and a.empid=${WF0310.P_empid};', '', NULL, '', '<p><br></p>', '', '<validate/>', NULL);

INSERT INTO mc_functions (ID, Code, Title, Title1, Title2, Title3, Title4, Title5, xType, GridLayout, FormLayout, ParamLayout, DataSource, SQLXML, AddNewScript, UpdateScript, DataPermission, DataCheck, ToolPermission, OprRemark, Remark, FormValidate, DBAlias) VALUES
(320517, 'WF0517', '离职代办/辞退流程', NULL, '', '', '', '', 'grid', '<grid column="1" refresh="1" toggle="0" pageswitch="0" checkbox="radiobox" pager="1" pagesize="15" pagelist="15,50,100" group="0" help="0" lockcolkey="" param="0" nowrap="0" height="100%" fixednumber="0" actionindex="1" toolstyle="1" edittype="0" importtplid="0" timestamp="0">\n  <cols colKey="ACTION" align="left" width="" sortable="0" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REGBYEMPNO" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REGORGCODE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DBTYPE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DBCONFIRM" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="P_EMPID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="EMPNO" align="left" width="80" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>1</uptlog>\n  </cols>\n  <cols colKey="TITLE" align="left" width="100" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>1</uptlog>\n  </cols>\n  <cols colKey="ORGPATH" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DEPCODE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="EMPTYPE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="POSTNAME" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="POSTDUTY" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="POSTM2" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CREATEDATE" align="left" width="100" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="WORKAGE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CONENDDATE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PARTY" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DISABLEDDATE" align="left" width="100" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>2</uptlog>\n  </cols>\n  <cols colKey="LEAVETYPE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="QRLZYYFL" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="SFZFJJBCJ" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="QRLZYYSM" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="LEAVEPLANTYPE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="LEAVEPLANNOTICETEXT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="EMAIL_P" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="MAILNOTICETEXT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHANGEATTACH" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="JJBCJJE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REMARK" align="left" width="150" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="DEPOPINION_SJ" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PROJECTRISK" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PROJECTRISKOPIN" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHECKINGOPINION" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHECKINGEXPLAIN" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="HROPINION_SJ" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISAUDIT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="HANDOVEREMP" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="BENEENDDATE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISWORKHANDOVER" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISPARENTCHECK" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISDEPCHECK" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="FINSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="TRAINSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ORGRELATIONSHIP" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="THEUNION" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="AUDITSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="AUDITSETTLEMENT_CNS" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="RETURNCOMPUTER" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="EMPCREDENTIALS" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISAUDITSETTLEMENT_CNS" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="LABORMANUAL" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CERTIFICATE" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="PAYSETTLEMENT" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="FLOWKEY" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="FLOWNO" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="INSTID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="STATUS" align="left" width="" sortable="1" visible="1" hidden="1" searchable="1" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="RESULTS" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="YJDISABLEDDATE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHANGEREASONDETAIL" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="CHANGEREASON" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="REGTIME" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="XTYPE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="WFINSTANCEID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISHANDOVERCHECK" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISHRBP" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ORGNAME" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISPOSTTYPE" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n</grid>', '<form>\n  <blocks id="28ut17vzzb9" type="block" layout="1" direction="0" border="0" showtype="1" rownum="59" colnum="2">\n    <cells code="REGBYEMPNO" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="REGORGCODE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="DBTYPE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="DBCONFIRM" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="P_EMPID" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells code="EMPNO" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="TITLE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="DEPCODE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="ORGPATH" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="EMPTYPE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="POSTNAME" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="POSTDUTY" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="POSTM2" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="CREATEDATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="WORKAGE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="PARTY" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="CONENDDATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="DISABLEDDATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="LEAVETYPE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="QRLZYYFL" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="SFZFJJBCJ" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells code="QRLZYYSM" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="LEAVEPLANTYPE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="LEAVEPLANNOTICETEXT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="EMAIL_P" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="MAILNOTICETEXT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CHANGEATTACH" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="2"/>\n    <cells code="JJBCJJE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells code="REMARK" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="HANDOVEREMP" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells code="ASSETREVIEWERS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ASSETNOTES" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="ASSETATTACH" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITREVIEWTEXT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="AUDITREVIEW" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CHECKINGEXPLAINV3" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="HROPINION_SJ" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITDEPARTURE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISWORKHANDOVER" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISPARENTCHECK" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISDEPCHECK" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="FINSETTLEMENT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="HOSPITALITY" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="TRAINSETTLEMENT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ORGRELATIONSHIP" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="THEUNION" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITSETTLEMENT_CNS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="AUDITSETTLEMENT" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="RETURNCOMPUTER" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="EMPCREDENTIALS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="ISAUDITSETTLEMENT_CNS" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="LABORMANUAL" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="CERTIFICATE" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="2"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="2"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n  </blocks>\n</form>', '', 'select a.* ,\n(SELECT CASE WHEN ifnull(b.PostType,0)=70119 THEN 1 ELSE 0 END FROM md_position b  WHERE a.postName=b.PostID ) AS isPostType,\n(select case when ifnull(b.emptype,0) in (1221,1214) then 1 else 0 end from md_employee b where a.P_empid=b.empid) as IsSx\n from wf_ht_empchange a where xtype=2;', '', 'select ${P_EMPID}  as RegByEmpno\n,now()  as RegTime,''ctlc''  as flowKey,1 as status,\n2 as xtype;\n\nselect 71031 as  CheckingOpinion , 71033 as AuditSettlement_Cns;\nselect depid  as RegOrgCode,orgid as  orgName\n from md_employee where empid=${p_empid};\n\n\n\nselect case when ifnull(b.emptype,0) in (1221,1214) then 1 else 0 end as IsSx\n from md_employee b where ${P_EMPID}=b.empid;\n\nSELECT CASE WHEN ifnull(b.PostType,0)=70119 THEN 1 ELSE 0 END isPostType\n FROM md_position b ,md_employee   a WHERE a.postid=b.PostID  and a.empid=${P_EMPID};\n\nselect ''邮箱用于接受《离职证明》，请务必确认准确'' as mailnoticetext;\nselect 1  as V1;\nselect 1  as V2;\nSelect Tempfilepath as Attach2 From mc_importtemplate Where id=3;', 'select empno,title,emptype,orgid as orgname,ofn_OrgDepid(orgid) as depcode,postid as postname,createdate,comyear as workage,party, postcustom4 as postduty,postm2,conenddate,email_p from md_employee where empid=${WF0517.P_empid};\n\nselect orgtpath as orgpath from ov_findorgline_exceptcomp where orgid=(select orgid from md_employee where empid=${WF0517.P_empid});\n\n\nselect 1 as ispromentor where exists(select * from md_employee a ,wf_ht_probreg b where a.empid=b.wfempid and b.promentor=${WF0517.P_empid} and ifnull(a.porbstatus,0)=70162 );\n\nselect cast(truncate(${wf0517.jjbcjje},2) as decimal(17,2)) as jjbcjje;\n\nselect ${WF0517.YjDisabledDate} as disableddate;\n\nselect case when ifnull(b.emptype,0) in (1221,1214) then 1 else 0 end as IsSx\n from md_employee b where ${WF0517.P_empid}=b.empid;\n\nSELECT CASE WHEN ifnull(b.PostType,0)=70119 THEN 1 ELSE 0 END isPostType\n FROM md_position b ,md_employee   a WHERE a.postid=b.PostID  and a.empid=${WF0517.P_empid};', '', NULL, NULL, '<p><br></p>', NULL, '<validate/>', NULL);


SET FOREIGN_KEY_CHECKS = 0; 