﻿-- 
-- Script was generated by Devart dbForge Studio for MySQL, Version *********
-- Product Home Page: http://www.devart.com/dbforge/mysql/studio
-- Script date 2025/6/27 17:55:29
-- Source server version: 8.0.28
-- Source connection string: User Id=rw_clehr;Host=*************;Port=3307;Database=clehr;Character Set=utf8
-- Target server version: 8.0.39
-- Target connection string: User Id=rw_clehr;Host=*************;Port=3308;Character Set=utf8
-- Run this script against clehr to synchronize it with clehr
-- 


--
-- Disable foreign keys
--
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;

;

DELIMITER $$

--
-- Create procedure `WfSp_CHECKINGOPINION2_BeforeCheck`
--
CREATE PROCEDURE WfSp_CHECKINGOPINION2_BeforeCheck(
		IN `P_instanceid` int,
		IN `P_nodeid` int,
		OUT `p_retval` varchar(500))
BEGIN

  DECLARE p_CheckingOpinion2 int;

  DECLARE p_error int DEFAULT 0;

  DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET p_error = 1;

  SET p_retval = 0;
	
	Select CheckingOpinion2 INTO p_CheckingOpinion2 From wf_ht_empchange 
	 Where WFINSTANCEID=P_instanceid;
	
	#司内调动，调入调出一级部门必须不同
	IF (		
		(Select COUNT(1) From mc_flowtasks Where InstanceID=P_instanceid 
		   And NodeID=46 
			 AND Approver IS NOT NULL)>=1 And p_CheckingOpinion2=71180 AND P_nodeid=46)
	Then 
		set p_retval=20131;
		set p_error=2;
	END IF;

  IF p_error = 1 THEN
    ROLLBACK;
		set p_retval= -1 ;
    SELECT
      p_retval;
  END IF;
	
  IF p_error = 2 THEN
    ROLLBACK;
    SELECT
      p_retval;
  END IF;

  COMMIT;
  SET autocommit = 1;



END
$$

DELIMITER ;

--
-- Enable foreign keys
--
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;