﻿-- CALL mcsp_faban('320321','',@abc)
-- ;SELECT @abc ;


SET FOREIGN_KEY_CHECKS = 0;

DELETE from mc_functions where id in (320321); -- 功能 
DELETE from mc_functioncols where fcid in (320321); -- 功能列
DELETE from mc_functiontools where fcid  in (320321); -- 功能按钮
DELETE from mc_functoolparams where fcid  in (320321); -- 功能按钮参数
DELETE from mc_functionparams  where fcid  in (320321); -- 功能参数
/*SELECT *  from mc_functions where id in (320321); -- 功能*/
 
/*SELECT *  from mc_functioncols where fcid in (320321); -- 功能列*/

/*SELECT *  from mc_functiontools where fcid  in (320321) ;-- 功能按钮*/

/*SELECT *  from mc_functoolparams where fcid  in  (320321)  ;-- 功能按钮参数*/

/*SELECT *  from mc_functionparams  where fcid  in  (320321)  ;-- 功能参数*/


/*
delete from mc_applications where id in ();  -- 应用
delete from mc_applicationparams where appid in ();  -- 应用参数
SELECT *  from mc_applications where id in ();  -- 应用
SELECT *  from mc_applicationparams where appid in ();  -- 应用参数

*/


INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60532, 320321, 'id', NULL, '', '', '', '', 'id', 'a', 'id', '', True, True, 10, 0, 11, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>1</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60533, 320321, 'WFInstanceID', NULL, '', '', '', '', 'WFInstanceID', 'a', 'WFInstanceID', '', False, False, 10, 0, 21, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60534, 320321, '交接人', NULL, '', '', '', '', 'handoverEmp', 'a', 'handoverEmp', '', False, False, 2550, 0, 31, '<col>\n  <datatype>Integer</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>SinglePerson</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[employee[onall]]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60535, 320321, '工作内容', NULL, '', '', '', '', 'TransferList', 'a', 'TransferList', '', False, False, 2550, 0, 41, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>TextArea</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(60536, 320321, '是否完成交接', NULL, '', '', '', '', 'IsHandover', 'a', 'IsHandover', '', False, False, 255, 0, 51, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>0</notnull>\n  <control>CheckBoxList</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[select code as id ,title from md_codes where id=70743]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73339, 320321, '交接文件', NULL, NULL, NULL, NULL, NULL, 'handoverAttach', 'a', 'handoverAttach', '', False, False, 63325, 0, 61, '<col>\n  <datatype>String</datatype>\n  <hidden>0</hidden>\n  <notnull>1</notnull>\n  <control>Attachment</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>4</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');

INSERT INTO mc_functioncols (ID, FCID, Title, Title1, Title2, Title3, Title4, Title5, ColName, ObjAlias, ColKey, Expression, IsPKey, IsIdentity, MaxLength, Scale, xOrder, Attribute) VALUES
(73340, 320321, 'V2', NULL, NULL, NULL, NULL, NULL, 'V2', 'a', 'V2', '', False, False, 10, 0, 71, '<col>\n  <datatype>Integer</datatype>\n  <hidden>1</hidden>\n  <notnull>0</notnull>\n  <control>TextBox</control>\n  <ctrlstyle></ctrlstyle>\n  <ctrlprop></ctrlprop>\n  <elscript><![CDATA[]]></elscript>\n  <format></format>\n  <defval><![CDATA[]]></defval>\n  <uploadtype>1</uploadtype>\n  <uploadfield></uploadfield>\n  <source><![CDATA[]]></source>\n  <customcode></customcode>\n  <locked>0</locked>\n  <crypt>0</crypt>\n</col>');


INSERT INTO mc_functions (ID, Code, Title, Title1, Title2, Title3, Title4, Title5, xType, GridLayout, FormLayout, ParamLayout, DataSource, SQLXML, AddNewScript, UpdateScript, DataPermission, DataCheck, ToolPermission, OprRemark, Remark, FormValidate, DBAlias) VALUES
(320321, 'WF0321', '离职工作交接', '', '', '', '', '', 'grid', '<grid column="1" refresh="1" toggle="0" pageswitch="0" checkbox="mcheckbox" pager="1" pagesize="15" pagelist="15,50,100" group="0" help="0" param="0" nowrap="0" height="100%" fixednumber="0" actionindex="0" toolstyle="0" edittype="1" importtplid="0" timestamp="0">\n  <cols colKey="ACTION" align="left" width="" sortable="0" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="WFINSTANCEID" align="left" width="" sortable="1" visible="1" hidden="1" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="HANDOVEREMP" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="TRANSFERLIST" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n  <cols colKey="ISHANDOVER" align="left" width="" sortable="1" visible="1" hidden="0" searchable="0" summary="" importKey="0" importVal="0" group="0" text="0">\n    <uptlog>0</uptlog>\n  </cols>\n</grid>', '<form>\n  <blocks id="7eg81fwwh6" type="block" layout="1" direction="0" border="0" showtype="1" rownum="4" colnum="2">\n    <cells code="HANDOVEREMP" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="1"/>\n    <cells hidetitle="0" titlenum="0" rows="1" cols="1"/>\n    <cells code="TRANSFERLIST" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="100" rows="1" cols="2"/>\n    <cells code="ISHANDOVER" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n    <cells code="HANDOVERATTACH" tips="" hidetitle="0" titlenum="1" titleclass="" contclass="" cellclass="" width="" height="" rows="1" cols="2"/>\n  </blocks>\n</form>', '', 'select a.* from wf_ht_empchange_subb a', '', 'Select 1 AS V2;', '', '', NULL, '', '<p><br></p>', '', '', NULL);

SET FOREIGN_KEY_CHECKS = 0; 