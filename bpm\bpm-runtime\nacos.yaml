spring:
  profiles:
    active: dev
  application:
    name: eip-bpm-runtime
  datasource:
    dynamic:
      datasource:
        master:
          username: <PERSON><PERSON>@hfbcoracle#cosco
          password: BPM_T#bpm0726
          driver-class-name: com.alipay.oceanbase.jdbc.Driver
          url: **************************************************************************************************************

  redis:
    host: *************
    port: 6379
    password: L3tMeIn!H@rd
  rabbitmq:
    addresses: **********:6672,**********:6672,**********:6672
    username: bpm-mq-test
    virtual-host: bpm-mq-test
    password: token:eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJicG0tbXEtdGVzdCJ9.YDHrOjP4gLtTDOvJSps_f9edcxsO3jHS6li00iO8irA

server:
  port: 8086

jms:
  enable: true

# 是否解决跨域问题
cors:
  enable: true

redis:
  enable: true

# 分布式事务
seata:
  enabled: false

eureka:
  client:
    service-url:
      defaultZone: http://127.0.0.1:8761/eureka/

xxl:
  job:
    enabled: false #如果使用xxl则不使用quartz
    admin:
      addresses: http://*************:8120/xxl-job-admin
    executor:
      port: 9997
      
transform:
  todo:
    enable: true
    token-url: https://gateway-clmp-gch-test.csleasing.com.cn/oauth/oauth/token
    create-url: https://gateway-clmp-gch-test.csleasing.com.cn/hitf/v1/rest/invoke?namespace=CLMP.PORTAL&serverCode=SYNC-TODOS&interfaceCode=clmp-portal.todo-sync.create
    update-url: https://gateway-clmp-gch-test.csleasing.com.cn/hitf/v1/rest/invoke?namespace=CLMP.PORTAL&serverCode=SYNC-TODOS&interfaceCode=clmp-portal.todo-sync.update
    delete-url: https://gateway-clmp-gch-test.csleasing.com.cn/hitf/v1/rest/invoke?namespace=CLMP.PORTAL&serverCode=SYNC-TODOS&interfaceCode=clmp-portal.todo-sync.removeSingle
    query-url: https://gateway-clmp-gch-test.csleasing.com.cn/hitf/v1/rest/invoke?namespace=CLMP.PORTAL&serverCode=SYNC-TODOS&interfaceCode=clmp-portal.todo-sync.querySourceIds
    importHy-url: http://*************:31267/GDDA/services/jaxrs/importHy/importCommonFile

    system-code: portal-it-service
    organization-id: 3

workflow:
  handUrl: http://************:5252/hls/modules/activiti/hls_activiti_wfl.lsc
  interfaceWay: ORACLE
  
clms:
  sendTask: true
  typekey: clms_csleasing

# aws OSS settings
aws:
  oss.accessKeyId: YWKZIPQD1MCW3W5EIS7D
  oss.accessKeySecret: 435xOuRR0bLKSC7MnmctYVjPCK53EB7lrGvn4V4G
  oss.bucketName: hotent

# 附件在线预览
file:
  file.dir: /home/<USER>/conver/
  office:
    # 是否启用openoffice组件（设置为true也需要在服务器上安装openoffice才能使用）
    enable: true
    home: /opt/openoffice4
    port: 9093
  simText: txt,html,xml,java,properties,sql
  media: mp3,mp4,flv,rmvb,wmv
  converted.file.charset: utf-8