create
    definer = rw_clehr@`%` procedure WfSp_empleavedo(IN p_instanceid int, IN p_type int, OUT p_retval varchar(500))
BEGIN   


  DECLARE p_error int DEFAULT 0;
	declare p_id int;
	declare p_emptype int;
	declare p_email varchar(200);
	declare p_empid int;


  DECLARE CONTINUE HANDLER FOR SQLEXCEPTION SET p_error = 1;
	
  SET p_retval = 0;
  SET autocommit = 0;
	
	select a.emptype,a.p_empid into p_emptype ,p_empid
	from wf_ht_empchange a  where a.wfinstanceid=p_instanceid;
		
		if p_type=1 then 
		
	insert into eu_empchange(wfinstanceid,xtype,empid,empno,title,orgid,postid,emptype,createdate,disableddate,lastworkdate,blacklist,regby,regtime,changetype,changereason,isovercontract,applyleavedate,changereasondetail,SeverancePayment,email_p) 
	select wfinstanceid,7,p_empid,empno,title,orgname,postname,emptype,createdate,disableddate,disableddate,0,regbyempno,regtime,leavetype,qrlzyyfl,1,regtime,ChangeReasonDetail,jjbcj<PERSON>,email_p
	from wf_ht_empchange 
	where wfinstanceid=p_instanceid;
	
	
	select id,email_p into p_id,p_email
	from eu_empchange 
	where ifnull(wfinstanceid,0)=p_instanceid;
	
	update md_employee a set email_p=p_email
	where empid=p_empid;


	-- 离职邮件
	call ep_empdo_email(p_id,null,7,p_retval);


	-- 离职本人邮件
	if p_emptype not in (1214,1221)
	then 
			call msp_mailto(2,p_id,p_retval);
	end if ;
	
	
	-- 离职外部邮件 830
	end if ;
	
		IF p_error = 1 THEN
			ROLLBACK;
			SET p_retval = -1;
			SELECT
				p_retval;
		END IF;

		IF p_error = 2 THEN
			ROLLBACK;
			SELECT
				p_retval;
		END IF;

		COMMIT;
		SET autocommit = 1;


END;

