redis:
  database: *
  host: bpm-redis-test
  port: 6379
  password: L3tMeIn!H@rd
rabbitmq:
  addresses: **********:6672,**********:6672,**********:6672
  username: bpm-mq-test
  virtual-host: bpm-mq-test
  password: token:eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJicG0tbXEtdGVzdCJ9.YDHrOjP4gLtTDOvJSps_f9edcxsO3jHS6li00iO8irA

username: UC@hfbcoracle#cosco
password: UC_T#bpm0726
driver-class-name: com.alipay.oceanbase.jdbc.Driver
url: jdbc:oceanbase://************:2883/UC

username: FORM@hfbcoracle#cosco
password: FORM_T#bpm0726
driver-class-name: com.alipay.oceanbase.jdbc.Driver
url: jdbc:oceanbase://************:2883/FORM

username: <PERSON><PERSON>@hfbcoracle#cosco
password: BPM_T#bpm0726
driver-class-name: com.alipay.oceanbase.jdbc.Driver
url: jdbc:oceanbase://************:2883/BPM

username: PORTAL@hfbcoracle#cosco
password: PORTAL_T#bpm0726
driver-class-name: com.alipay.oceanbase.jdbc.Driver
url: jdbc:oceanbase://************:2883/PORTAL
