
SELECT a.empid AS id ,CONCAT(b.Empno,'-',b.Title,'-',a.title) AS title ,a.Disabled 
FROM eu_assetreviewers a ,md_employee b WHERE a.empid=b.EmpID and ifnull(a.disabled,0)=0 ORDER BY a.xorder

SELECT a.empid AS id ,CONCAT('资产审核人','-',b.Empno,'-',b.Title,'(',a.title,'); ','资产复核人','-',c.Empno,'-',c.title) AS title ,a.Disabled FROM eu_assetreviewers a ,md_employee b,md_employee c WHERE a.empid=b.EmpID and a.fgempid = c.EmpID and ifnull(a.disabled,0)=0 ORDER BY a.xorder


SELECT a.empid AS id ,CONCAT(b.Empno,'-',b.Title,'-',a.title,'；',c.Empno, '-',c.Title,'-',a.title) AS title ,a.Disabled FROM eu_assetreviewers a ,md_employee b,md_employee c WHERE a.empid=b.EmpID and a.fgempid = c.EmpID and ifnull(a.disabled,0)=0 ORDER BY a.xorder

hide(leavetype,${WF0310.results} == 1);
hide(disableddate,${WF0310.results} == 1);
hide(QRLZYYFL,${WF0310.results} == 1);
hide(QRLZYYSM,${WF0310.results} == 1);
hide(SFZFJJBCJ,${WF0310.results} == 1);
hide(SFZFJJBCJ,${WF0310.results} == 1);
hide(LEAVEPLANTYPE,${WF0310.results} == 1);
hide(LEAVEPLANNOTICETEXT,${WF0310.results} == 1);
hide(P_EMPID,${WF0310.ISHRBP}==0);
hide(JJBCJJE,${WF0310.SFZFJJBCJ} == 0);
hide(REMARK,${WF0310.SFZFJJBCJ} == 0);
lock(TAG1,${WF0310.P_EMPID} != -1);
hide(isAUDITSETTLEMENT_CNS,${WF0310.AUDITSETTLEMENT_CNS} != 71034);
hide(ORGNAME,${WF0310.P_EMPID} != 0);
hide(depOpinion_sj,${WF0310.V1} == 1);
hide(ProjectRisk,${WF0310.isPostType} == 0||${WF0310.V1} == 1);
hide(ProjectRiskOpin,${WF0310.isPostType} == 0||${WF0310.V1} == 1);
hide(CheckingOpinion,${WF0310.V1} == 1);
hide(CheckingExplain,${WF0310.V1} == 1);
hide(isaudit,${WF0310.V1} == 1);
hide(AuditDeparture,${WF0310.HROPINION_SJ} != 1 || ${WF0310.V1} == 0);
hide(AssetReviewers,${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);
hide(AssetNotes,${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);
hide(AssetAttach,${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);
hide(AssetOverdue,${WF0310.isPostType} == 0|| ${WF0310.V1} == 0);
hide(AuditReview,${WF0310.ASSETOVERDUE} != 71167 || ${WF0310.V1} == 0);
hide(Hospitality,${WF0310.V1} == 0);
hide(V1,${WF0310.P_EMPID} != 0);
hide(Attach2,${WF0310.V2} == 0);
hide(CHECKINGOPINION2,${WF0310.V2} == 0);
hide(V2,${WF0310.P_EMPID} != 0);
hide(AssetOverdue, ${WF0310.V3} == 1);


稽核资产审计 
hide(${WF0310.ASSETOVERDUE} != 71167 || ${WF0310.V1} == 0);


select 1 as TC;select 1 as TC;select 1 as TC;WFSP_FlowTaskTo_Id(${WF_TASKID},1);WFSP_FlowTaskTo_JSON(${PARAM4},1);api_MH_TASK1(${PARAM5});EP_OARESULT_TO(${PARAM4},1,${PARAM5},${PARAM6});

select 1 ;select 2;XMSP_Api_NEW_LZJY_JSON(${WF_InstanceID});api_NEW_XM_LZJY(${PARAM3});XMSP_Api_LZJY_DOV3(${WF_InstanceID},${PARAM1},${PARAM2},${PARAM3},${PARAM4});

select 1 ;select 2;XMSP_Api_NEW_LZJY_JSON(${WF_InstanceID});api_NEW_XM_LZJY(${PARAM3});XMSP_Api_LZJY_DOV3(${WF_InstanceID},${PARAM1},${PARAM2},${PARAM3},${PARAM4});

71163 启动   
71164 不启动
select case when ifnull(${WF0310.AuditReview},71164)=71163 then 0 else 1 end 
select case when ifnull(${WF0310.AuditReview},71164)=71163 then 1 else 0 end 

select case when ifnull(${WF0571.AuditReview},71164)=71163 then 0 else 1 end
select case when ifnull(${WF0571.AuditReview},71164)=71163 then 1 else 0 end

中后台 Select case when ${WF0310.isPostType} =1 then 1 else 0 end ;
前台 Select case when ${WF0310.isPostType} =1 then 0 else 1 end ;

hide(${WF0310.isPostType} == 0 || ${WF0310.V1} == 0);

hide(CHECKINGEXPLAINV3, ${WF0310.AuditReview} != 71163 );

SELECT FlowVersion,* FROM mc_flowinstances WHERE id=57465
SELECT FlowVersion,* FROM mc_flowtasks WHERE InstanceID=57465
SELECT FlowVersion,* FROM mc_flownotices WHERE InstanceID=57465


----

人员  ---- 考勤制度
1. 主数据 人员  工号 岗位 岗位编码 考勤制度
2. 岗位 属性 
3. 考勤表 员工工号 姓名 考勤制度
4. 底层表
